/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Package network provides comprehensive network intelligence collection capabilities for Eino.
//
// The network package offers components for analyzing URLs, domains, and IP addresses to collect
// various types of network information including:
//
//   - DNS resolution and analysis
//   - TLS/SSL certificate information and security assessment
//   - WHOIS data collection and parsing
//   - CDN detection and provider identification
//   - Geolocation information
//   - Website fingerprinting and technology stack detection
//   - ASN (Autonomous System Number) information
//   - Security assessments and threat intelligence
//   - Website screenshots
//
// # Basic Usage
//
// Create a network analyzer and perform analysis:
//
//	analyzer := network.NewAnalyzer(
//		network.WithGeoLocation(true),
//		network.WithTLSSecurity(true),
//		network.WithFingerprint(true),
//	)
//
//	result, err := analyzer.Analyze(ctx, "https://example.com")
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	fmt.Printf("Domain: %s, IP: %s, CDN: %s\n", 
//		result.Domain, result.IP, result.CDN.Provider)
//
// # Batch Processing
//
// Process multiple URLs concurrently:
//
//	urls := []string{
//		"https://example.com",
//		"https://google.com",
//		"https://github.com",
//	}
//
//	results, err := analyzer.BatchAnalyze(ctx, urls,
//		network.WithConcurrency(10, 5))
//
// # Integration with Eino Chains
//
// Use network analysis as part of an Eino chain:
//
//	chain := eino.NewChain[string, *schema.NetworkResult]().
//		AppendNetworkAnalyzer(analyzer).
//		AppendChatModel(llm).  // AI analysis of results
//		Compile(ctx)
//
//	result, err := chain.Invoke(ctx, "https://suspicious-site.com")
//
// # Streaming Analysis
//
// For real-time processing or large batches:
//
//	stream, err := analyzer.Stream(ctx, "https://example.com")
//	if err != nil {
//		log.Fatal(err)
//	}
//
//	for {
//		result, err := stream.Recv()
//		if err == io.EOF {
//			break
//		}
//		if err != nil {
//			log.Printf("Error: %v", err)
//			continue
//		}
//		// Process result
//		fmt.Printf("Received result for: %s\n", result.Domain)
//	}
//
// # Configuration Options
//
// The network analyzer supports extensive configuration:
//
//	analyzer := network.NewAnalyzer(
//		// Enable/disable specific analysis types
//		network.WithGeoLocation(true),
//		network.WithTLSSecurity(true),
//		network.WithFingerprint(true),
//		network.WithScreenshot(true, "png", 1920, 1080, true),
//		network.WithThreatIntel(true),
//
//		// Network configuration
//		network.WithTimeout(30*time.Second),
//		network.WithRetry(3),
//		network.WithProxy("http://proxy:8080", "user", "pass"),
//
//		// Batch processing
//		network.WithConcurrency(10, 5),
//
//		// Advanced options
//		network.WithDNSServers([]string{"8.8.8.8", "1.1.1.1"}),
//		network.WithCustomHeaders(map[string]string{
//			"X-Custom": "value",
//		}),
//	)
//
// # Use Cases
//
// The network analyzer is designed for various use cases:
//
//   - Security Analysis: Assess domain security posture, detect malicious infrastructure
//   - Asset Discovery: Identify and catalog network assets and their configurations
//   - Threat Intelligence: Collect IOCs (Indicators of Compromise) and threat data
//   - Compliance Checking: Verify TLS configurations and security standards
//   - Brand Protection: Detect phishing sites and brand abuse
//   - Network Monitoring: Track changes in network infrastructure
//   - AI-Powered Analysis: Combine with LLMs for intelligent threat assessment
//
// # Integration with AI Models
//
// The network analyzer is designed to work seamlessly with AI models:
//
//	// Create an AI-powered security analysis pipeline
//	graph := eino.NewGraph[string, *SecurityReport]()
//
//	// Network intelligence collection
//	graph.AddNetworkAnalyzerNode("collect", analyzer)
//
//	// AI threat analysis
//	graph.AddChatModelNode("analyze", securityLLM)
//
//	// Report generation
//	graph.AddLambdaNode("report", generateSecurityReport)
//
//	// Connect the pipeline
//	graph.AddEdge(eino.START, "collect")
//	graph.AddEdge("collect", "analyze")
//	graph.AddEdge("analyze", "report")
//	graph.AddEdge("report", eino.END)
//
//	compiled, err := graph.Compile(ctx)
//	report, err := compiled.Invoke(ctx, "https://suspicious-domain.com")
//
// This package integrates the powerful network intelligence capabilities originally from
// the mcpcllectionbyurl project into the Eino framework, providing a unified interface
// for network analysis within AI-powered applications.
package network

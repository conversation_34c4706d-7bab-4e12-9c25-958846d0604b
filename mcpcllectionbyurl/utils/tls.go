package utils

import (
	"context"
	"crypto/tls"
	"encoding/pem"
	"fmt"
	"net"
	"os/exec"
	"time"
)

func FetchTLSInfo(host string) (pemStr, opensslRaw string) {
	dialTarget := net.JoinHostPort(host, "443")
	cfg := &tls.Config{InsecureSkipVerify: true, ServerName: host}
	conn, err := tls.DialWithDialer(&net.Dialer{Timeout: 5 * time.Second}, "tcp", dialTarget, cfg)
	if err != nil {
		return "", fmt.Sprintf("tls dial error: %v", err)
	}
	defer conn.Close()

	state := conn.ConnectionState()
	if len(state.PeerCertificates) == 0 {
		pemStr = "no certificate returned"
	} else {
		pemBytes := pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: state.PeerCertificates[0].Raw})
		pemStr = string(pemBytes)
	}

	// Use openssl s_client (optional)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	cmd := exec.CommandContext(ctx, "openssl", "s_client", "-servername", host, "-connect", dialTarget, "-brief")
	rawBytes, _ := cmd.CombinedOutput()
	opensslRaw = string(rawBytes)
	return
}

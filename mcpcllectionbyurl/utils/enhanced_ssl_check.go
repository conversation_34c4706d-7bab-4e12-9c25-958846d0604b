package utils

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"net"
	"os/exec"
	"regexp"
	"strings"
	"time"
)

// EnhancedTLSSecurityResult 增强的TLS安全评估结果
type EnhancedTLSSecurityResult struct {
	TLSSecurityResult
	ConnectionSuccessful bool                // 连接是否成功
	ErrorMessage         string              // 错误信息
	UsedMethod           string              // 使用的方法（Go TLS, OpenSSL, GnuTLS）
	CertificateChain     []*x509.Certificate // 证书链
	TLSExtensions        []string            // TLS扩展
	ALPN                 []string            // ALPN协议
	OCSP                 bool                // OCSP Stapling
	CT                   bool                // 证书透明度
	SessionTickets       bool                // 会话票证
	SNI                  bool                // 服务器名称指示
	RetryCount           int                 // 重试次数
	TotalTime            int                 // 总耗时(ms)
}

// EnhancedTLSCheck 增强的TLS安全检查
func EnhancedTLSCheck(host string, port int) (*EnhancedTLSSecurityResult, error) {
	startTime := time.Now()

	result := &EnhancedTLSSecurityResult{
		TLSSecurityResult: TLSSecurityResult{
			Host:            host,
			Port:            port,
			CertificateInfo: make(map[string]string),
			Vulnerabilities: make(map[TLSVulnerability]bool),
		},
		ConnectionSuccessful: false,
		RetryCount:           0,
	}

	// 尝试使用不同的方法进行TLS检查
	methods := []string{"go-tls", "openssl", "gnutls"}
	var lastError error

	for _, method := range methods {
		// 最多重试3次
		for retry := 0; retry < 3; retry++ {
			result.RetryCount++

			var err error
			switch method {
			case "go-tls":
				err = checkWithGoTLS(host, port, result)
			case "openssl":
				err = checkWithOpenSSL(host, port, result)
			case "gnutls":
				err = checkWithGnuTLS(host, port, result)
			}

			if err == nil {
				// 成功连接
				result.ConnectionSuccessful = true
				result.UsedMethod = method
				break
			} else {
				lastError = err
				Logger.Debug("TLS检查失败 (方法: %s, 重试: %d): %v", method, retry, err)
				// 等待一段时间再重试
				time.Sleep(500 * time.Millisecond)
			}
		}

		if result.ConnectionSuccessful {
			break
		}
	}

	// 如果所有方法都失败，返回错误
	if !result.ConnectionSuccessful {
		result.ErrorMessage = lastError.Error()

		// 尝试使用TCP连接检查端口是否开放
		if isPortOpen(host, port) {
			Logger.Info("端口 %d 开放，但TLS握手失败", port)

			// 尝试使用curl命令作为最后的回退方案
			if curlResult, err := checkWithCurl(host, port); err == nil {
				result.ConnectionSuccessful = true
				result.UsedMethod = "curl"

				// 设置基本信息
				if curlResult.Protocol != "" {
					switch curlResult.Protocol {
					case "TLSv1.0":
						result.SupportedVersions = append(result.SupportedVersions, TLSv10)
					case "TLSv1.1":
						result.SupportedVersions = append(result.SupportedVersions, TLSv11)
					case "TLSv1.2":
						result.SupportedVersions = append(result.SupportedVersions, TLSv12)
					case "TLSv1.3":
						result.SupportedVersions = append(result.SupportedVersions, TLSv13)
					}
				}

				// 设置证书信息
				if curlResult.CertificateInfo != nil {
					for k, v := range curlResult.CertificateInfo {
						result.CertificateInfo[k] = v
					}
				}

				// 设置密码套件
				if curlResult.CipherName != "" {
					result.CipherSuites = append(result.CipherSuites, TLSCipherSuite{
						Name:     curlResult.CipherName,
						Strength: getCipherStrength(curlResult.CipherName),
					})
				}
			} else {
				Logger.Debug("Curl检查失败: %v", err)
			}
		} else {
			Logger.Info("端口 %d 未开放", port)
		}
	}

	// 计算安全评级
	if result.ConnectionSuccessful {
		result.SecurityRating = calculateSecurityRating(&result.TLSSecurityResult)

		// 生成安全建议
		result.Recommendations = generateSecurityRecommendations(&result.TLSSecurityResult)
	}

	// 计算总耗时
	result.TotalTime = int(time.Since(startTime).Milliseconds())

	return result, nil
}

// checkWithGoTLS 使用Go的TLS库进行检查
func checkWithGoTLS(host string, port int, result *EnhancedTLSSecurityResult) error {
	// 使用默认超时

	// 创建拨号器
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}

	// 尝试TLS 1.0, 1.1, 1.2, 1.3
	versions := []uint16{tls.VersionTLS10, tls.VersionTLS11, tls.VersionTLS12, tls.VersionTLS13}
	supportedVersions := []TLSVersion{}

	for _, version := range versions {
		config := &tls.Config{
			MinVersion:         version,
			MaxVersion:         version,
			InsecureSkipVerify: true, // 跳过证书验证，我们只关心连接是否成功
			ServerName:         host, // 设置SNI
		}

		// 尝试连接，最多重试3次
		var conn *tls.Conn
		var err error
		for retry := 0; retry < 3; retry++ {
			conn, err = tls.DialWithDialer(dialer, "tcp", fmt.Sprintf("%s:%d", host, port), config)
			if err == nil {
				break
			}
			time.Sleep(500 * time.Millisecond)
		}

		if err != nil {
			continue
		}

		// 连接成功，记录支持的版本
		switch version {
		case tls.VersionTLS10:
			supportedVersions = append(supportedVersions, TLSv10)
		case tls.VersionTLS11:
			supportedVersions = append(supportedVersions, TLSv11)
		case tls.VersionTLS12:
			supportedVersions = append(supportedVersions, TLSv12)
		case tls.VersionTLS13:
			supportedVersions = append(supportedVersions, TLSv13)
		}

		// 如果是第一次成功连接，获取证书信息
		if len(result.CertificateInfo) == 0 && conn != nil {
			// 获取证书链
			certs := conn.ConnectionState().PeerCertificates
			if len(certs) > 0 {
				result.CertificateChain = certs

				// 获取主证书信息
				cert := certs[0]
				result.CertificateInfo["Subject"] = cert.Subject.String()
				result.CertificateInfo["Issuer"] = cert.Issuer.String()
				result.CertificateInfo["NotBefore"] = cert.NotBefore.Format("2006-01-02 15:04:05")
				result.CertificateInfo["NotAfter"] = cert.NotAfter.Format("2006-01-02 15:04:05")
				result.CertificateInfo["SerialNumber"] = cert.SerialNumber.String()
				result.CertificateInfo["Version"] = fmt.Sprintf("%d", cert.Version)

				// 检查证书是否过期
				now := time.Now()
				if now.Before(cert.NotBefore) {
					result.CertificateInfo["Status"] = "未生效"
				} else if now.After(cert.NotAfter) {
					result.CertificateInfo["Status"] = "已过期"
				} else {
					result.CertificateInfo["Status"] = "有效"
				}

				// 获取SAN扩展
				var sans []string
				for _, dnsName := range cert.DNSNames {
					sans = append(sans, dnsName)
				}
				result.CertificateInfo["SubjectAltName"] = strings.Join(sans, ", ")

				// 检查OCSP Stapling
				result.OCSP = conn.ConnectionState().OCSPResponse != nil

				// 检查证书透明度
				result.CT = len(conn.ConnectionState().VerifiedChains) > 0 &&
					len(conn.ConnectionState().SignedCertificateTimestamps) > 0

				// 检查会话票证
				result.SessionTickets = conn.ConnectionState().DidResume

				// 检查ALPN
				if conn.ConnectionState().NegotiatedProtocol != "" {
					result.ALPN = []string{conn.ConnectionState().NegotiatedProtocol}
				}
			}

			// 获取密码套件
			cipherSuite := tls.CipherSuiteName(conn.ConnectionState().CipherSuite)
			result.CipherSuites = append(result.CipherSuites, TLSCipherSuite{
				Name:     cipherSuite,
				Strength: getCipherStrength(cipherSuite),
			})

			// 关闭连接
			conn.Close()
		}
	}

	// 设置支持的TLS版本
	result.SupportedVersions = supportedVersions

	// 检查漏洞
	result.Vulnerabilities[Heartbleed] = checkHeartbleed(host, port)
	result.Vulnerabilities[POODLE] = containsTLSVersion(supportedVersions, SSLv3)
	result.Vulnerabilities[BEAST] = containsTLSVersion(supportedVersions, TLSv10)

	return nil
}

// checkWithOpenSSL 使用OpenSSL进行检查
func checkWithOpenSSL(host string, port int, result *EnhancedTLSSecurityResult) error {
	// 设置超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 执行OpenSSL命令，添加更多选项以提高成功率
	cmd := exec.CommandContext(ctx, "openssl", "s_client",
		"-connect", fmt.Sprintf("%s:%d", host, port),
		"-servername", host,
		"-no_ssl3",       // 禁用SSLv3
		"-no_tls1_3",     // 某些服务器可能不支持TLS 1.3
		"-cipher", "ALL", // 尝试所有密码套件
		"-ciphersuites", "ALL", // 尝试所有TLS 1.3密码套件
		"-timeout", "5", // 设置超时
		"-verify_return_error", // 验证错误时返回
	)

	// 执行命令，最多重试3次
	var output []byte
	var err error
	for retry := 0; retry < 3; retry++ {
		output, err = cmd.CombinedOutput()
		if err == nil {
			break
		}
		time.Sleep(500 * time.Millisecond)
	}

	if err != nil {
		return fmt.Errorf("OpenSSL命令执行失败: %v", err)
	}

	// 解析输出
	outputStr := string(output)

	// 检查是否成功连接
	if !strings.Contains(outputStr, "CONNECTED") {
		return fmt.Errorf("OpenSSL连接失败")
	}

	// 解析协议版本
	protocolRegex := regexp.MustCompile(`Protocol\s+:\s+([^\s]+)`)
	protocolMatches := protocolRegex.FindStringSubmatch(outputStr)
	if len(protocolMatches) > 1 {
		protocol := protocolMatches[1]
		switch protocol {
		case "TLSv1":
			result.SupportedVersions = append(result.SupportedVersions, TLSv10)
		case "TLSv1.1":
			result.SupportedVersions = append(result.SupportedVersions, TLSv11)
		case "TLSv1.2":
			result.SupportedVersions = append(result.SupportedVersions, TLSv12)
		case "TLSv1.3":
			result.SupportedVersions = append(result.SupportedVersions, TLSv13)
		case "SSLv3":
			result.SupportedVersions = append(result.SupportedVersions, SSLv3)
		}
	}

	// 解析密码套件
	cipherRegex := regexp.MustCompile(`Cipher\s+:\s+(.+)`)
	cipherMatches := cipherRegex.FindStringSubmatch(outputStr)
	if len(cipherMatches) > 1 {
		cipherName := strings.TrimSpace(cipherMatches[1])
		result.CipherSuites = append(result.CipherSuites, TLSCipherSuite{
			Name:     cipherName,
			Strength: getCipherStrength(cipherName),
		})
	}

	// 解析证书信息
	if len(result.CertificateInfo) == 0 {
		// 提取主题
		subjectRegex := regexp.MustCompile(`subject=([^\n]+)`)
		subjectMatches := subjectRegex.FindStringSubmatch(outputStr)
		if len(subjectMatches) > 1 {
			result.CertificateInfo["Subject"] = subjectMatches[1]
		}

		// 提取颁发者
		issuerRegex := regexp.MustCompile(`issuer=([^\n]+)`)
		issuerMatches := issuerRegex.FindStringSubmatch(outputStr)
		if len(issuerMatches) > 1 {
			result.CertificateInfo["Issuer"] = issuerMatches[1]
		}

		// 提取有效期
		validityRegex := regexp.MustCompile(`notBefore=([^\n]+).*notAfter=([^\n]+)`)
		validityMatches := validityRegex.FindStringSubmatch(outputStr)
		if len(validityMatches) > 2 {
			result.CertificateInfo["NotBefore"] = validityMatches[1]
			result.CertificateInfo["NotAfter"] = validityMatches[2]
		}
	}

	return nil
}

// checkWithGnuTLS 使用GnuTLS进行检查
func checkWithGnuTLS(host string, port int, result *EnhancedTLSSecurityResult) error {
	// 设置超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 执行GnuTLS命令，添加更多选项以提高成功率
	cmd := exec.CommandContext(ctx, "gnutls-cli",
		"--insecure",                  // 跳过证书验证
		"--priority=NORMAL:+VERS-ALL", // 尝试所有TLS版本
		"--timeout=5",                 // 设置超时
		"-p", fmt.Sprintf("%d", port),
		host)

	// 执行命令，最多重试3次
	var output []byte
	var err error
	for retry := 0; retry < 3; retry++ {
		output, err = cmd.CombinedOutput()
		if err == nil {
			break
		}
		time.Sleep(500 * time.Millisecond)
	}

	if err != nil {
		return fmt.Errorf("GnuTLS命令执行失败: %v", err)
	}

	// 解析输出
	outputStr := string(output)

	// 检查是否成功连接
	if !strings.Contains(outputStr, "Handshake was completed") {
		return fmt.Errorf("GnuTLS连接失败")
	}

	// 解析协议版本
	protocolRegex := regexp.MustCompile(`Protocol: ([^\s,]+)`)
	protocolMatches := protocolRegex.FindStringSubmatch(outputStr)
	if len(protocolMatches) > 1 {
		protocol := protocolMatches[1]
		switch protocol {
		case "TLS1.0":
			result.SupportedVersions = append(result.SupportedVersions, TLSv10)
		case "TLS1.1":
			result.SupportedVersions = append(result.SupportedVersions, TLSv11)
		case "TLS1.2":
			result.SupportedVersions = append(result.SupportedVersions, TLSv12)
		case "TLS1.3":
			result.SupportedVersions = append(result.SupportedVersions, TLSv13)
		case "SSL3.0":
			result.SupportedVersions = append(result.SupportedVersions, SSLv3)
		}
	}

	// 解析密码套件
	cipherRegex := regexp.MustCompile(`Cipher: ([^\s]+)`)
	cipherMatches := cipherRegex.FindStringSubmatch(outputStr)
	if len(cipherMatches) > 1 {
		cipherName := strings.TrimSpace(cipherMatches[1])
		result.CipherSuites = append(result.CipherSuites, TLSCipherSuite{
			Name:     cipherName,
			Strength: getCipherStrength(cipherName),
		})
	}

	return nil
}

// isPortOpen 检查端口是否开放
func isPortOpen(host string, port int) bool {
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), 5*time.Second)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// getCipherStrength 获取密码套件强度
func getCipherStrength(cipherName string) string {
	cipherName = strings.ToUpper(cipherName)

	// 弱密码套件
	if strings.Contains(cipherName, "NULL") ||
		strings.Contains(cipherName, "EXPORT") ||
		strings.Contains(cipherName, "DES") ||
		strings.Contains(cipherName, "RC4") {
		return "低"
	}

	// 强密码套件
	if strings.Contains(cipherName, "AES256") ||
		strings.Contains(cipherName, "CHACHA20") ||
		strings.Contains(cipherName, "POLY1305") {
		return "高"
	}

	// 中等密码套件
	return "中"
}

// containsTLSVersion 检查TLS版本切片是否包含指定版本
func containsTLSVersion(slice []TLSVersion, item TLSVersion) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

// CurlTLSResult curl TLS检查结果
type CurlTLSResult struct {
	Protocol        string            // TLS协议版本
	CipherName      string            // 密码套件名称
	CertificateInfo map[string]string // 证书信息
}

// checkWithCurl 使用curl命令检查TLS
func checkWithCurl(host string, port int) (*CurlTLSResult, error) {
	// 设置超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 执行curl命令，添加更多选项以提高成功率
	cmd := exec.CommandContext(ctx, "curl",
		"-v",              // 详细输出
		"-k",              // 跳过证书验证
		"-s",              // 静默模式
		"-o", "/dev/null", // 丢弃输出
		"--connect-timeout", "5", // 连接超时
		fmt.Sprintf("https://%s:%d", host, port))

	// 执行命令，最多重试3次
	var output []byte
	var err error
	for retry := 0; retry < 3; retry++ {
		output, err = cmd.CombinedOutput()
		if err == nil || strings.Contains(string(output), "SSL connection") {
			break
		}
		time.Sleep(500 * time.Millisecond)
	}

	// 即使命令返回错误，我们也尝试解析输出
	outputStr := string(output)

	// 检查是否成功建立SSL连接
	if !strings.Contains(outputStr, "SSL connection") {
		return nil, fmt.Errorf("Curl未能建立SSL连接")
	}

	result := &CurlTLSResult{
		CertificateInfo: make(map[string]string),
	}

	// 解析协议版本
	protocolRegex := regexp.MustCompile(`SSL connection using ([^\s]+)`)
	protocolMatches := protocolRegex.FindStringSubmatch(outputStr)
	if len(protocolMatches) > 1 {
		result.Protocol = protocolMatches[1]
	}

	// 解析密码套件
	cipherRegex := regexp.MustCompile(`SSL connection using [^\s]+ / ([^\s]+)`)
	cipherMatches := cipherRegex.FindStringSubmatch(outputStr)
	if len(cipherMatches) > 1 {
		result.CipherName = cipherMatches[1]
	}

	// 解析证书信息
	subjectRegex := regexp.MustCompile(`subject: ([^\n]+)`)
	subjectMatches := subjectRegex.FindStringSubmatch(outputStr)
	if len(subjectMatches) > 1 {
		result.CertificateInfo["Subject"] = subjectMatches[1]
	}

	issuerRegex := regexp.MustCompile(`issuer: ([^\n]+)`)
	issuerMatches := issuerRegex.FindStringSubmatch(outputStr)
	if len(issuerMatches) > 1 {
		result.CertificateInfo["Issuer"] = issuerMatches[1]
	}

	// 解析证书有效期
	startDateRegex := regexp.MustCompile(`start date: ([^\n]+)`)
	startDateMatches := startDateRegex.FindStringSubmatch(outputStr)
	if len(startDateMatches) > 1 {
		result.CertificateInfo["NotBefore"] = startDateMatches[1]
	}

	expireDateRegex := regexp.MustCompile(`expire date: ([^\n]+)`)
	expireDateMatches := expireDateRegex.FindStringSubmatch(outputStr)
	if len(expireDateMatches) > 1 {
		result.CertificateInfo["NotAfter"] = expireDateMatches[1]
	}

	return result, nil
}

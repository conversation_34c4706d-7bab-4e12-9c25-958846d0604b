/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"context"

	"github.com/cloudwego/eino/components/network/schema"
)

// NetworkAnalyzer defines the interface for network intelligence collection.
// It provides methods for analyzing URLs/domains and collecting various network information
// including DNS, TLS, WHOIS, CDN, geolocation, and security assessments.
//
//go:generate mockgen -destination ../../internal/mock/components/network/NetworkAnalyzer_mock.go --package network -source interface.go
type NetworkAnalyzer interface {
	// Analyze performs comprehensive network analysis on a single URL/domain
	Analyze(ctx context.Context, input string, opts ...Option) (*schema.NetworkResult, error)

	// Batch<PERSON>naly<PERSON> performs analysis on multiple URLs/domains concurrently
	BatchAnalyze(ctx context.Context, inputs []string, opts ...Option) ([]*schema.NetworkResult, error)
}

// TODO: Add streaming interfaces when integrating with eino's streaming system
// type NetworkStreamAnalyzer interface {
//     NetworkAnalyzer
//     Stream(ctx context.Context, input string, opts ...Option) (*schema.StreamReader[*schema.NetworkResult], error)
//     StreamBatch(ctx context.Context, inputs []string, opts ...Option) (*schema.StreamReader[*schema.NetworkResult], error)
// }

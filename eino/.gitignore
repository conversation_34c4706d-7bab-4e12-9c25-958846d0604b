# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# env file
.env

# the result of the go build
output*
output/*

# Files generated by IDEs
.idea/
*.iml

# Vim swap files
*.swp

# Vscode files
.vscode

/patches

/vendor


/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"sync"
	"time"

	"github.com/cloudwego/eino/components"
	"github.com/cloudwego/eino/components/network/internal/asn"
	"github.com/cloudwego/eino/components/network/internal/dns"
	tlsAnalyzer "github.com/cloudwego/eino/components/network/internal/tls"
	"github.com/cloudwego/eino/components/network/schema"
)

// Analyzer implements the NetworkAnalyzer interface
type Analyzer struct {
	config      *Config
	dnsResolver *dns.Resolver
	asnLookup   *asn.Lookup
	tlsAnalyzer *tlsAnalyzer.Analyzer
}

// NewAnalyzer creates a new network analyzer with the given options
func NewAnalyzer(opts ...Option) *Analyzer {
	config := DefaultConfig()
	ApplyOptions(config, opts...)

	return &Analyzer{
		config:      config,
		dnsResolver: dns.NewResolver(config.DNSServers, config.Timeout),
		asnLookup:   asn.NewLookup(config.Timeout),
		tlsAnalyzer: tlsAnalyzer.NewAnalyzer(config.Timeout, !config.VerifySSL),
	}
}

// GetType returns the component type name
func (a *Analyzer) GetType() string {
	return "NetworkAnalyzer"
}

// IsCallbacksEnabled returns whether callbacks are enabled
func (a *Analyzer) IsCallbacksEnabled() bool {
	return false // Let the framework handle callbacks
}

// Analyze performs comprehensive network analysis on a single URL/domain
func (a *Analyzer) Analyze(ctx context.Context, input string, opts ...Option) (*schema.NetworkResult, error) {
	startTime := time.Now()

	// Apply runtime options
	config := *a.config // Copy config
	ApplyOptions(&config, opts...)

	// Parse URL
	parsedURL, err := url.Parse(input)
	if err != nil {
		return nil, fmt.Errorf("failed to parse URL: %w", err)
	}

	// If no scheme specified, default to HTTPS
	if parsedURL.Scheme == "" {
		input = "https://" + input
		parsedURL, err = url.Parse(input)
		if err != nil {
			return nil, fmt.Errorf("failed to parse URL with scheme: %w", err)
		}
	}

	host := parsedURL.Hostname()
	isIP := dns.IsIPAddress(host)
	port := parsedURL.Port()

	// Set default port if not specified
	portNum := 443
	if port != "" {
		portNum, _ = strconv.Atoi(port)
	} else if parsedURL.Scheme == "http" {
		portNum = 80
	}

	// Create result structure
	result := &schema.NetworkResult{
		Domain:     host,
		IsIPAccess: isIP,
		Port:       portNum,
		Timestamp:  time.Now().Unix(),
		Success:    true,
		Metadata:   make(map[string]string),
	}

	// Perform DNS analysis
	if config.EnableDNSAnalysis && !isIP {
		dnsInfo, err := a.dnsResolver.ResolveDomain(ctx, host)
		if err != nil {
			result.Error = fmt.Sprintf("DNS resolution failed: %v", err)
			result.Success = false
		} else {
			result.DNS = dnsInfo
			if len(dnsInfo.ARecords) > 0 {
				result.IP = dnsInfo.ARecords[0]
			}
		}
	} else if isIP {
		// For IP addresses, set the IP directly
		result.IP = host
		result.DNS = &schema.DNSInfo{
			ARecords: []string{host},
		}
	}

	// Perform ASN lookup
	if result.IP != "" {
		asnInfo, err := a.asnLookup.LookupASN(ctx, result.IP)
		if err != nil {
			// Don't fail the entire analysis for ASN lookup failure
			result.ASN = &schema.IPASNInfo{
				ASN:     "ERROR",
				ISP:     "",
				RawData: fmt.Sprintf("ASN lookup failed: %v", err),
			}
		} else {
			result.ASN = asnInfo
		}
	}

	// Perform TLS analysis if enabled and we have an IP
	if config.EnableTLSSecurity && result.IP != "" {
		tlsPem, tlsSecurityInfo, err := a.tlsAnalyzer.AnalyzeTLS(ctx, host, portNum)
		if err != nil {
			// Don't fail the entire analysis for TLS failure
			result.TLS = fmt.Sprintf("TLS analysis failed: %v", err)
		} else {
			result.TLS = tlsPem
			if result.Advanced == nil {
				result.Advanced = &schema.AdvancedAnalysis{}
			}
			result.Advanced.TLSSecurity = tlsSecurityInfo
		}
	}

	// TODO: Add other analysis modules
	// - WHOIS lookup
	// - CDN detection
	// - Geolocation
	// - Fingerprinting
	// - Screenshot capture

	// Calculate analysis time
	result.AnalysisTime = int(time.Since(startTime).Milliseconds())

	return result, nil
}

// TODO: Implement streaming when integrating with eino's streaming system
// Stream performs network analysis and returns results as a stream
// func (a *Analyzer) Stream(ctx context.Context, input string, opts ...Option) (*schema.StreamReader[*schema.NetworkResult], error) {
//     result, err := a.Analyze(ctx, input, opts...)
//     if err != nil {
//         return nil, err
//     }
//     // Create a stream reader that returns the single result
//     return schema.NewSingleResultStream(result), nil
// }

// BatchAnalyze performs analysis on multiple URLs/domains concurrently
func (a *Analyzer) BatchAnalyze(ctx context.Context, inputs []string, opts ...Option) ([]*schema.NetworkResult, error) {
	config := *a.config // Copy config
	ApplyOptions(&config, opts...)

	results := make([]*schema.NetworkResult, len(inputs))
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, config.MaxConcurrency)

	for i, input := range inputs {
		wg.Add(1)
		go func(index int, url string) {
			defer wg.Done()

			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			result, err := a.Analyze(ctx, url, opts...)
			if err != nil {
				// Create error result
				result = &schema.NetworkResult{
					Domain:    url,
					Success:   false,
					Error:     err.Error(),
					Timestamp: time.Now().Unix(),
				}
			}
			results[index] = result
		}(i, input)
	}

	wg.Wait()
	return results, nil
}

// TODO: Implement streaming batch analysis when integrating with eino's streaming system
// StreamBatch performs batch analysis and returns results as a stream
// func (a *Analyzer) StreamBatch(ctx context.Context, inputs []string, opts ...Option) (*schema.StreamReader[*schema.NetworkResult], error) {
//     // Implementation will be added when integrating with eino's streaming system
//     return nil, nil
// }

// firstOrEmpty returns the first element of a slice or empty string if slice is empty
func firstOrEmpty(slice []string) string {
	if len(slice) > 0 {
		return slice[0]
	}
	return ""
}

// Ensure Analyzer implements the required interfaces
var _ NetworkAnalyzer = (*Analyzer)(nil)

// TODO: Add streaming interface checks when implementing streaming
// var _ NetworkStreamAnalyzer = (*Analyzer)(nil)
var _ components.Typer = (*Analyzer)(nil)
var _ components.Checker = (*Analyzer)(nil)

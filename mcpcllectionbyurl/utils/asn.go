package utils

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mcpcllectionbyurl/models"
	"net"
	"net/http"
	"strings"
	"time"
)

// asnAPIResponse IPAPI.com ASN响应结构
type asnAPIResponse struct {
	Status      string `json:"status"`
	ASN         string `json:"as"`
	ISP         string `json:"isp"`
	Organization string `json:"org"`
	Message     string `json:"message"`
}

// LookupASNForIP 查询IP地址的ASN信息
func LookupASNForIP(ipStr string) models.IPASNInfo {
	if ipStr == "" {
		return models.IPASNInfo{ASN: "NA", ISP: "No IP Address"}
	}

	// 解析IP地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return models.IPASNInfo{ASN: "ERROR", ISP: "Invalid IP Address"}
	}

	// 检查是否为私有IP
	if isPrivateIP(ip) {
		return models.IPASNInfo{
			ASN:     "PRIVATE",
			ISP:     "Private Network",
			RawData: "Private IP Address Range",
		}
	}

	// 检查是否为IPv6
	if ip.To4() == nil {
		// 处理IPv6地址
		return lookupIPv6ASN(ipStr)
	}

	// 使用IP-API.com查询ASN信息
	return lookupIPv4ASN(ipStr)
}

// lookupIPv4ASN 查询IPv4地址的ASN信息
func lookupIPv4ASN(ipStr string) models.IPASNInfo {
	// 使用IP-API.com查询ASN信息
	apiURL := fmt.Sprintf("http://ip-api.com/json/%s?fields=status,message,as,isp,org", ipStr)
	
	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	
	resp, err := client.Get(apiURL)
	if err != nil {
		return models.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("API请求失败: %v", err),
		}
	}
	defer resp.Body.Close()
	
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return models.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("读取API响应失败: %v", err),
		}
	}
	
	var apiResp asnAPIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return models.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("解析API响应失败: %v", err),
		}
	}
	
	// 检查API响应状态
	if apiResp.Status != "success" {
		return models.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("API返回错误: %s", apiResp.Message),
		}
	}
	
	// 提取ASN编号
	asn := "AS-UNKNOWN"
	if apiResp.ASN != "" {
		// 通常AS格式为"AS12345 Some ISP"，我们只需要AS部分
		parts := strings.Split(apiResp.ASN, " ")
		if len(parts) > 0 {
			asn = parts[0]
		}
	}
	
	return models.IPASNInfo{
		ASN:     asn,
		ISP:     apiResp.ISP,
		RawData: string(body),
	}
}

// lookupIPv6ASN 查询IPv6地址的ASN信息
func lookupIPv6ASN(ipStr string) models.IPASNInfo {
	// 使用IP-API.com查询ASN信息（同样支持IPv6）
	apiURL := fmt.Sprintf("http://ip-api.com/json/%s?fields=status,message,as,isp,org", ipStr)
	
	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	
	resp, err := client.Get(apiURL)
	if err != nil {
		return models.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("IPv6 API请求失败: %v", err),
		}
	}
	defer resp.Body.Close()
	
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return models.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("读取IPv6 API响应失败: %v", err),
		}
	}
	
	var apiResp asnAPIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return models.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("解析IPv6 API响应失败: %v", err),
		}
	}
	
	// 检查API响应状态
	if apiResp.Status != "success" {
		return models.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("IPv6 API返回错误: %s", apiResp.Message),
		}
	}
	
	// 提取ASN编号
	asn := "AS-UNKNOWN"
	if apiResp.ASN != "" {
		// 通常AS格式为"AS12345 Some ISP"，我们只需要AS部分
		parts := strings.Split(apiResp.ASN, " ")
		if len(parts) > 0 {
			asn = parts[0]
		}
	}
	
	return models.IPASNInfo{
		ASN:     asn,
		ISP:     apiResp.ISP,
		RawData: string(body),
	}
}

# Network Analysis Example

This example demonstrates how to use the Eino network analysis component to collect comprehensive network intelligence from URLs and domains.

## Features

The network analyzer provides:

- **DNS Resolution**: A records, CNAME records, MX records, TXT records
- **ASN Information**: Autonomous System Number, ISP details
- **Geolocation**: Country, city, coordinates (when enabled)
- **TLS Security**: Certificate analysis and security assessment (when enabled)
- **Website Fingerprinting**: Technology stack detection (when enabled)
- **Batch Processing**: Concurrent analysis of multiple URLs

## Usage

### Single URL Analysis

```bash
# Basic analysis
go run main.go -url=https://example.com

# Disable certain features
go run main.go -url=example.com -geo=false -tls=false

# Save output to file
go run main.go -url=https://github.com -output=result.json

# Verbose output
go run main.go -url=https://google.com -verbose
```

### Batch Analysis

```bash
# Analyze multiple URLs from file
go run main.go -batch=sample-urls.txt

# Increase concurrency
go run main.go -batch=sample-urls.txt -concurrency=10

# Save batch results
go run main.go -batch=sample-urls.txt -output=batch-results.json -verbose
```

### Command Line Options

- `-url`: Single URL to analyze
- `-batch`: File containing URLs (one per line)
- `-output`: Output file (default: stdout)
- `-format`: Output format (currently only json)
- `-concurrency`: Max concurrent requests for batch processing (default: 5)
- `-geo`: Enable geolocation analysis (default: true)
- `-tls`: Enable TLS security analysis (default: true)
- `-fingerprint`: Enable website fingerprinting (default: true)
- `-timeout`: Request timeout (default: 30s)
- `-verbose`: Enable verbose output

## Sample Output

```json
{
  "domain": "example.com",
  "ip": "*************",
  "is_ip_access": false,
  "port": 443,
  "dns": {
    "a_records": ["*************"],
    "dns_server_used": "system"
  },
  "whois": null,
  "asn": {
    "asn": "AS15133",
    "isp": "Edgecast Inc.",
    "raw_data": "{\"status\":\"success\",\"as\":\"AS15133 Edgecast Inc.\",\"isp\":\"Edgecast Inc.\",\"org\":\"Edgecast Inc.\"}"
  },
  "cdn": null,
  "tls_pem": "",
  "advanced": null,
  "timestamp": 1640995200,
  "analysis_time_ms": 1250,
  "success": true
}
```

## Integration with Eino Framework

This example shows how to use the network analyzer as a standalone component. In a real Eino application, you can integrate it with other components:

```go
// Example: Combine network analysis with AI analysis
chain := eino.NewChain[string, *SecurityReport]().
    AppendNetworkAnalyzer(analyzer).
    AppendChatModel(securityLLM).
    AppendLambda(generateReport).
    Compile(ctx)

report, err := chain.Invoke(ctx, "https://suspicious-domain.com")
```

## Building and Running

```bash
# Build the example
go build -o network-analysis main.go

# Run with sample URLs
./network-analysis -batch=sample-urls.txt -verbose

# Or run directly with go
go run main.go -url=https://example.com
```

## Use Cases

- **Security Analysis**: Assess domain security posture
- **Asset Discovery**: Catalog network infrastructure
- **Threat Intelligence**: Collect IOCs and threat data
- **Brand Protection**: Detect phishing and brand abuse
- **Network Monitoring**: Track infrastructure changes
- **AI-Powered Analysis**: Feed data to LLMs for intelligent assessment

## Notes

- Some features (geolocation, TLS analysis) may require external API calls
- Rate limiting may apply to external services
- Private IP addresses are detected and handled appropriately
- The analyzer respects timeouts and provides error handling
- Batch processing uses controlled concurrency to avoid overwhelming targets

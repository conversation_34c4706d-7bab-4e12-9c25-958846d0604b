package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"mcpcllectionbyurl/handler"
	"mcpcllectionbyurl/models"
	"mcpcllectionbyurl/utils"
	"os"
	"strings"
)

func main() {
	// 解析命令行参数
	var (
		inputURL          string
		outputFile        string
		verbose           bool
		debug             bool
		batchFile         string
		showVersion       bool
		outputFormat      string
		maxConcurrent     int
		proxyURL          string
		proxyUser         string
		proxyPass         string
		enableGeoLocation bool
		enableTLSSecurity bool
		enableFingerprint bool
		enableScreenshot  bool
		screenshotFormat  string
		screenshotWidth   int
		screenshotHeight  int
		screenshotFull    bool
	)

	// 基本选项
	flag.StringVar(&inputURL, "url", "", "要分析的URL")
	flag.StringVar(&outputFile, "output", "", "结果输出文件路径 (默认: 标准输出)")
	flag.StringVar(&outputFormat, "format", "json", "输出格式 (json 或 csv)")
	flag.BoolVar(&verbose, "verbose", false, "启用详细输出")
	flag.BoolVar(&debug, "debug", false, "启用调试日志")
	flag.StringVar(&batchFile, "batch", "", "从文件批量处理多个URL (每行一个URL)")
	flag.IntVar(&maxConcurrent, "concurrent", 5, "批处理时的最大并发数")

	// 代理选项
	flag.StringVar(&proxyURL, "proxy", "", "HTTP代理URL (格式: http://host:port)")
	flag.StringVar(&proxyUser, "proxy-user", "", "HTTP代理用户名")
	flag.StringVar(&proxyPass, "proxy-pass", "", "HTTP代理密码")

	// 高级分析选项
	flag.BoolVar(&enableGeoLocation, "geo", true, "启用地理位置信息")
	flag.BoolVar(&enableTLSSecurity, "tls-check", true, "启用TLS安全评估")
	flag.BoolVar(&enableFingerprint, "fingerprint", true, "启用网站指纹识别")
	flag.BoolVar(&enableScreenshot, "screenshot", false, "启用网站截图")
	flag.StringVar(&screenshotFormat, "screenshot-format", "png", "截图格式 (png 或 jpeg)")
	flag.IntVar(&screenshotWidth, "screenshot-width", 1280, "截图宽度")
	flag.IntVar(&screenshotHeight, "screenshot-height", 800, "截图高度")
	flag.BoolVar(&screenshotFull, "screenshot-full", true, "截取整个页面")

	// 其他选项
	flag.BoolVar(&showVersion, "version", false, "显示版本信息")
	flag.Parse()

	// 设置日志级别
	if debug {
		utils.Logger.SetLevel(utils.DEBUG)
	} else if verbose {
		utils.Logger.SetLevel(utils.INFO)
	} else {
		utils.Logger.SetLevel(utils.WARN)
	}

	// 显示版本信息
	if showVersion {
		fmt.Println("MCP Collection By URL v1.2.0")
		fmt.Println("Copyright © 2023-2025")
		os.Exit(0)
	}

	// 验证输出格式
	if outputFormat != "json" && outputFormat != "csv" {
		utils.Logger.Fatal("不支持的输出格式: %s (支持的格式: json, csv)", outputFormat)
	}

	// 设置HTTP代理
	if proxyURL != "" {
		if err := utils.SetGlobalProxy(proxyURL, proxyUser, proxyPass); err != nil {
			utils.Logger.Fatal("设置HTTP代理失败: %v", err)
		}
		utils.Logger.Info("已启用HTTP代理: %s", proxyURL)
	}

	// 创建MCP选项
	mcpOptions := handler.MCPOptions{
		EnableGeoLocation:  enableGeoLocation,
		EnableTLSSecurity:  enableTLSSecurity,
		EnableFingerprint:  enableFingerprint,
		EnableScreenshot:   enableScreenshot,
		ScreenshotFormat:   screenshotFormat,
		ScreenshotWidth:    screenshotWidth,
		ScreenshotHeight:   screenshotHeight,
		ScreenshotFullPage: screenshotFull,
	}

	// 处理批处理文件
	if batchFile != "" {
		processBatchFile(batchFile, outputFile, outputFormat, maxConcurrent, mcpOptions)
		return
	}

	// 检查是否提供了URL
	if inputURL == "" {
		if len(os.Args) > 1 && !strings.HasPrefix(os.Args[1], "-") {
			inputURL = os.Args[1]
		} else {
			fmt.Println("用法: mcpcllectionbyurl -url=https://example.com")
			fmt.Println("   或: mcpcllectionbyurl https://example.com")
			fmt.Println("\n基本选项:")
			fmt.Println("  -url=<url>                要分析的URL")
			fmt.Println("  -output=<file>            结果输出文件路径")
			fmt.Println("  -format=<format>          输出格式 (json 或 csv)")
			fmt.Println("  -batch=<file>             从文件批量处理多个URL")
			fmt.Println("  -concurrent=<num>         批处理时的最大并发数")
			fmt.Println("  -verbose                  启用详细输出")
			fmt.Println("  -debug                    启用调试日志")
			fmt.Println("\n代理选项:")
			fmt.Println("  -proxy=<url>              HTTP代理URL")
			fmt.Println("  -proxy-user=<user>        HTTP代理用户名")
			fmt.Println("  -proxy-pass=<pass>        HTTP代理密码")
			fmt.Println("\n高级分析选项:")
			fmt.Println("  -geo=<bool>               启用地理位置信息")
			fmt.Println("  -tls-check=<bool>         启用TLS安全评估")
			fmt.Println("  -fingerprint=<bool>       启用网站指纹识别")
			fmt.Println("  -screenshot=<bool>        启用网站截图")
			fmt.Println("  -screenshot-format=<fmt>  截图格式 (png 或 jpeg)")
			fmt.Println("  -screenshot-width=<px>    截图宽度")
			fmt.Println("  -screenshot-height=<px>   截图高度")
			fmt.Println("  -screenshot-full=<bool>   截取整个页面")
			fmt.Println("\n其他选项:")
			fmt.Println("  -version                  显示版本信息")
			fmt.Println("\n示例:")
			fmt.Println("  mcpcllectionbyurl -url=https://example.com -output=result.json")
			fmt.Println("  mcpcllectionbyurl -batch=urls.txt -concurrent=10 -format=csv -output=results.csv")
			fmt.Println("  mcpcllectionbyurl -url=https://example.com -screenshot=true -screenshot-format=jpeg")
			os.Exit(1)
		}
	}

	// 处理URL
	utils.Logger.Info("正在分析URL: %s", inputURL)
	result, err := handler.HandleMCPRequestWithOptions(inputURL, mcpOptions)
	if err != nil {
		utils.Logger.Fatal("处理URL时出错: %v", err)
	}

	// 导出结果
	if outputFile != "" {
		exportOptions := utils.ExportOptions{
			Format:     utils.ExportFormat(outputFormat),
			OutputFile: outputFile,
			Pretty:     true,
		}

		if err := utils.ExportResults([]*models.MCPResponse{result}, exportOptions); err != nil {
			utils.Logger.Fatal("导出结果时出错: %v", err)
		}

		utils.Logger.Info("结果已写入到 %s", outputFile)
	} else {
		// 直接输出到控制台
		jsonData, err := json.MarshalIndent(result, "", "  ")
		if err != nil {
			utils.Logger.Fatal("转换结果为JSON时出错: %v", err)
		}
		fmt.Println(string(jsonData))
	}
}

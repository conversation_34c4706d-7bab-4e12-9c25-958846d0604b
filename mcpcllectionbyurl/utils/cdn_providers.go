package utils

import (
	"strings"
)

// CDNProvider CDN提供商信息
type CDNProvider struct {
	Name        string   // CDN提供商名称
	HeaderRules []string // HTTP头规则
	CNameRules  []string // CNAME规则
}

// CDNProviders 已知的CDN提供商列表
var CDNProviders = []CDNProvider{
	{
		Name: "Cloudflare",
		HeaderRules: []string{
			"cf-ray",
			"cf-cache-status",
			"cloudflare",
		},
		CNameRules: []string{
			"cloudflare.com",
			"cloudflare.net",
		},
	},
	{
		Name: "Akamai",
		HeaderRules: []string{
			"x-akamai-transformed",
			"akamai",
		},
		CNameRules: []string{
			"akamai.net",
			"akamaiedge.net",
			"akamaihd.net",
			"edgesuite.net",
			"edgekey.net",
		},
	},
	{
		Name: "Fastly",
		HeaderRules: []string{
			"x-fastly",
			"fastly",
		},
		CNameRules: []string{
			"fastly.net",
		},
	},
	{
		Name: "Amazon CloudFront",
		HeaderRules: []string{
			"x-amz-cf-id",
			"cloudfront",
		},
		CNameRules: []string{
			"cloudfront.net",
		},
	},
	{
		Name: "Google Cloud CDN",
		HeaderRules: []string{
			"x-goog-",
		},
		CNameRules: []string{
			"googleusercontent.com",
		},
	},
	{
		Name: "Microsoft Azure CDN",
		HeaderRules: []string{
			"x-azure-ref",
		},
		CNameRules: []string{
			"azureedge.net",
			"msecnd.net",
		},
	},
	{
		Name: "Alibaba Cloud CDN",
		HeaderRules: []string{
			"x-swift-",
		},
		CNameRules: []string{
			"alicdn.com",
			"aliyuncs.com",
		},
	},
	{
		Name: "Tencent Cloud CDN",
		HeaderRules: []string{
			"x-daa-",
		},
		CNameRules: []string{
			"qcloud.com",
			"tencent-cloud.net",
		},
	},
	{
		Name: "Baidu Cloud CDN",
		HeaderRules: []string{
			"x-bce-",
		},
		CNameRules: []string{
			"bdydns.com",
			"bcebos.com",
		},
	},
	{
		Name: "Verizon Edgecast",
		HeaderRules: []string{
			"x-ec-",
		},
		CNameRules: []string{
			"edgecastcdn.net",
		},
	},
	{
		Name: "Limelight",
		HeaderRules: []string{
			"x-ll-",
		},
		CNameRules: []string{
			"limelight.com",
			"llnwd.net",
		},
	},
	{
		Name: "StackPath",
		HeaderRules: []string{
			"x-sp-",
		},
		CNameRules: []string{
			"stackpathdns.com",
		},
	},
	{
		Name: "Imperva Incapsula",
		HeaderRules: []string{
			"x-iinfo",
			"incap_ses",
		},
		CNameRules: []string{
			"incapdns.net",
		},
	},
	{
		Name: "Sucuri",
		HeaderRules: []string{
			"x-sucuri-",
		},
		CNameRules: []string{
			"sucuri.net",
		},
	},
	{
		Name: "Cachefly",
		HeaderRules: []string{
			"x-cf1",
		},
		CNameRules: []string{
			"cachefly.net",
		},
	},
}

// DetectCDNProvider 根据HTTP头和CNAME记录检测CDN提供商
func DetectCDNProvider(headers map[string]string, cnames []string) (provider string, confidence int) {
	// 初始化置信度
	confidence = 0

	// 检查HTTP头
	for _, cdnProvider := range CDNProviders {
		for _, headerRule := range cdnProvider.HeaderRules {
			for header, value := range headers {
				if strings.Contains(strings.ToLower(header), headerRule) ||
					strings.Contains(strings.ToLower(value), headerRule) {
					provider = cdnProvider.Name
					confidence += 50
					break
				}
			}
		}
	}

	// 检查CNAME记录
	if len(cnames) > 0 {
		for _, cdnProvider := range CDNProviders {
			for _, cnameRule := range cdnProvider.CNameRules {
				for _, cname := range cnames {
					if strings.Contains(strings.ToLower(cname), cnameRule) {
						if provider == "" || provider == cdnProvider.Name {
							provider = cdnProvider.Name
							confidence += 50
						} else {
							// 如果已经检测到不同的CDN提供商，可能是多CDN架构
							return "Multiple CDNs", 30
						}
						break
					}
				}
			}
		}
	}

	// 如果没有检测到CDN提供商，但有一些常见的CDN相关头
	if provider == "" {
		cdnHeaders := []string{"x-cache", "x-served-by", "x-cache-hits", "cache-control", "via"}
		for _, cdnHeader := range cdnHeaders {
			if _, ok := headers[cdnHeader]; ok {
				return "Unknown CDN", 20
			}
		}
	}

	// 限制最大置信度为100
	if confidence > 100 {
		confidence = 100
	}

	return
}

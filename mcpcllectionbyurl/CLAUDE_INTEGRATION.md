# 在Claude中使用MCP工具

本文档介绍如何在Claude中调用MCP (Multi-Channel Profiling) 工具，以获取域名和URL的各种网络情报信息。

## 前提条件

1. 已经构建并运行了MCP API服务器（通过Docker或本地运行）
2. API服务器可以从Claude访问（如果使用本地服务器，需要确保它可以从互联网访问）

## 方法1：使用Python客户端

### 在Claude中使用Python代码调用MCP

```python
import requests
import json

def analyze_domain(url, api_url="http://your-mcp-api-url:8080", tls_check=True, fingerprint=True):
    """使用MCP API分析域名或URL"""
    params = {
        "url": url,
    }
    
    if tls_check:
        params["tls_check"] = "true"
    
    if fingerprint:
        params["fingerprint"] = "true"
    
    try:
        response = requests.get(f"{api_url}/api/analyze", params=params)
        response.raise_for_status()
        
        result = response.json()
        if not result.get("success"):
            return {"error": result.get("error")}
        
        return result.get("data")
    except Exception as e:
        return {"error": str(e)}

# 使用示例
domain = "www.example.com"
result = analyze_domain(domain)

if "error" in result:
    print(f"分析失败: {result['error']}")
else:
    print(f"域名: {result.get('domain')}")
    print(f"IP地址: {result.get('ip')}")
    
    # 打印CDN信息
    if "cdn_info" in result and result["cdn_info"]:
        cdn_info = result["cdn_info"]
        print(f"CDN提供商: {cdn_info.get('provider')}")
        print(f"CDN置信度: {cdn_info.get('confidence')}%")
    
    # 打印TLS安全信息
    if "advanced_analysis" in result and "tls_security" in result["advanced_analysis"]:
        tls = result["advanced_analysis"]["tls_security"]
        print(f"TLS安全评级: {tls.get('security_rating')}")
        print(f"支持的TLS版本: {', '.join(tls.get('supported_versions', []))}")
```

## 方法2：使用JavaScript调用MCP

### 在Claude中使用JavaScript代码调用MCP

```javascript
async function analyzeDomain(url, apiUrl = "http://your-mcp-api-url:8080", tlsCheck = true, fingerprint = true) {
  // 构建请求URL
  const params = new URLSearchParams({
    url: url
  });
  
  if (tlsCheck) {
    params.append("tls_check", "true");
  }
  
  if (fingerprint) {
    params.append("fingerprint", "true");
  }
  
  try {
    // 发送请求
    const response = await fetch(`${apiUrl}/api/analyze?${params.toString()}`);
    
    if (!response.ok) {
      throw new Error(`HTTP错误! 状态码: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (!result.success) {
      return { error: result.error };
    }
    
    return result.data;
  } catch (error) {
    return { error: error.message };
  }
}

// 使用示例
async function main() {
  const domain = "www.example.com";
  const result = await analyzeDomain(domain);
  
  if (result.error) {
    console.error(`分析失败: ${result.error}`);
  } else {
    console.log(`域名: ${result.domain}`);
    console.log(`IP地址: ${result.ip}`);
    
    // 打印CDN信息
    if (result.cdn_info) {
      console.log(`CDN提供商: ${result.cdn_info.provider}`);
      console.log(`CDN置信度: ${result.cdn_info.confidence}%`);
    }
    
    // 打印TLS安全信息
    if (result.advanced_analysis && result.advanced_analysis.tls_security) {
      const tls = result.advanced_analysis.tls_security;
      console.log(`TLS安全评级: ${tls.security_rating}`);
      console.log(`支持的TLS版本: ${tls.supported_versions.join(", ")}`);
    }
  }
}

main();
```

## 方法3：使用Claude的工具调用功能

如果您使用的是支持工具调用的Claude版本，您可以定义一个工具来调用MCP：

### 1. 定义MCP工具

```json
{
  "name": "analyze_domain",
  "description": "使用MCP工具分析域名或URL，获取DNS、TLS、WHOIS、CDN等信息",
  "parameters": {
    "type": "object",
    "properties": {
      "url": {
        "type": "string",
        "description": "要分析的URL或域名"
      },
      "tls_check": {
        "type": "boolean",
        "description": "是否执行TLS安全评估",
        "default": true
      },
      "fingerprint": {
        "type": "boolean",
        "description": "是否执行网站指纹识别",
        "default": true
      }
    },
    "required": ["url"]
  }
}
```

### 2. 实现工具处理函数

在您的后端代码中，实现处理此工具调用的函数，该函数应该调用MCP API并返回结果。

## 最佳实践

1. **缓存结果**：MCP分析可能需要一些时间，考虑缓存结果以提高性能。
2. **错误处理**：确保处理API可能返回的各种错误情况。
3. **超时设置**：设置合理的超时时间，避免长时间等待。
4. **结果解析**：根据您的需求，只提取和展示相关的信息，而不是整个结果。

## 示例用例

1. **域名安全评估**：分析域名的TLS配置和安全评级。
2. **CDN检测**：检测网站是否使用CDN，以及使用的是哪种CDN。
3. **技术栈分析**：分析网站使用的技术栈，包括服务器类型、CMS、编程语言等。
4. **仿冒网站检测**：比较可疑网站与合法网站的特征，检测潜在的仿冒行为。

FROM golang:1.20 as builder

WORKDIR /app
COPY . .

# 构建MCP工具
RUN go build -o mcpcllectionbyurl

# 构建API服务器
RUN go build -o mcp_api mcp_api.go

# 使用轻量级基础镜像
FROM debian:bullseye-slim

# 安装必要的依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    openssl \
    curl \
    whois \
    dnsutils \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/mcpcllectionbyurl /app/
COPY --from=builder /app/mcp_api /app/

# 暴露API端口
EXPOSE 8080

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/api/health || exit 1

# 启动API服务器
CMD ["/app/mcp_api"]

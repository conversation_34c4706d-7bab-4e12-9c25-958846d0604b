package handler

import (
	"fmt"
	"mcpcllectionbyurl/models"
	"mcpcllectionbyurl/utils"
	"net/url"
	"os"
	"strconv"
	"strings"
)

// MCPOptions MCP请求选项
type MCPOptions struct {
	EnableGeoLocation  bool   // 启用地理位置信息
	EnableTLSSecurity  bool   // 启用TLS安全评估
	EnableFingerprint  bool   // 启用网站指纹识别
	EnableScreenshot   bool   // 启用网站截图
	ScreenshotFormat   string // 截图格式（png/jpeg）
	ScreenshotWidth    int    // 截图宽度
	ScreenshotHeight   int    // 截图高度
	ScreenshotFullPage bool   // 是否截取整个页面
}

// DefaultMCPOptions 返回默认MCP选项
func DefaultMCPOptions() MCPOptions {
	return MCPOptions{
		EnableGeoLocation:  true,
		EnableTLSSecurity:  true,
		EnableFingerprint:  true,
		EnableScreenshot:   false,
		ScreenshotFormat:   "png",
		ScreenshotWidth:    1280,
		ScreenshotHeight:   800,
		ScreenshotFullPage: true,
	}
}

// HandleMCPRequest 处理URL并返回MCP（多通道分析）信息
func HandleMCPRequest(inputURL string) (*models.MCPResponse, error) {
	return HandleMCPRequestWithOptions(inputURL, DefaultMCPOptions())
}

// HandleMCPRequestWithOptions 使用自定义选项处理URL并返回MCP信息
func HandleMCPRequestWithOptions(inputURL string, options MCPOptions) (*models.MCPResponse, error) {
	// 解析URL
	parsedURL, err := url.Parse(inputURL)
	if err != nil {
		return nil, fmt.Errorf("URL解析失败: %w", err)
	}

	// 如果没有指定协议，默认使用HTTPS
	if parsedURL.Scheme == "" {
		inputURL = "https://" + inputURL
		parsedURL, err = url.Parse(inputURL)
		if err != nil {
			return nil, fmt.Errorf("URL解析失败: %w", err)
		}
	}

	host := parsedURL.Hostname()
	isIP := utils.IsBareIP(host)
	port := parsedURL.Port()

	// 如果没有指定端口，根据协议设置默认端口
	portNum := 443
	if port != "" {
		portNum, _ = strconv.Atoi(port)
	} else if parsedURL.Scheme == "http" {
		portNum = 80
	}

	utils.Logger.Info("开始处理URL: %s (主机: %s, 端口: %d)", inputURL, host, portNum)

	// TLS
	tlsPem, opensslRaw := utils.FetchTLSInfo(host)

	// WHOIS
	whoisText, company, whoisSuccess, whoisCost := "", (*models.CompanyizedWhoisResq)(nil), false, 0
	if !isIP {
		// 域名WHOIS查询
		whoisText, company, whoisSuccess, whoisCost = utils.GetWhoisAndCompany(host)
	} else {
		// IP地址WHOIS查询
		whoisText, company, whoisSuccess, whoisCost = utils.GetIPWhoisInfo(host)
	}

	// DNS / ASN / CDN
	var aRecords []string
	var dnsServer string
	var asnInfo models.IPASNInfo
	var cdnHeaders, rawHeaders map[string]string
	if !isIP {
		aRecords, dnsServer = utils.ResolveDomain(host)
		asnInfo = utils.LookupASNForIP(firstOrEmpty(aRecords))
	} else {
		// 如果是IP地址，直接查询ASN
		asnInfo = utils.LookupASNForIP(host)
		// 对于IP地址，将其作为A记录
		aRecords = []string{host}
	}

	// 无论是域名还是IP地址，都获取HTTP头信息
	cdnHeaders, rawHeaders = utils.FetchCDNHeaders(host)

	// 获取CDN信息
	cdnInfo := utils.GetCDNInfo(host)

	// 创建基本响应
	response := &models.MCPResponse{
		CollectionDomainResq: models.CollectionDomainResq{
			Domain:     host,
			IP:         firstOrEmpty(aRecords),
			IsIPAccess: isIP,
			TLSPem:     tlsPem,
			OpenSSLRaw: opensslRaw,
		},
		CollectionWhoisResq: models.CollectionWhoisResq{
			RawWhois:       whoisText,
			WhoisRuntimeMs: whoisCost,
			Success:        whoisSuccess,
			Company:        company,
		},
		CollectionDNSResponse: models.CollectionDNSResponse{
			ARecords:      aRecords,
			DNSServerUsed: dnsServer,
			IPToASN:       asnInfo,
			CDNHeaders:    cdnHeaders,
			RawHeaders:    rawHeaders,
			CDNInfo: &models.CDNInfo{
				Provider:   cdnInfo.Provider,
				Confidence: cdnInfo.Confidence,
				Headers:    cdnInfo.Headers,
				CNames:     cdnInfo.CNames,
			},
		},
		AdvancedAnalysis: &models.AdvancedAnalysisInfo{},
	}

	// 高级分析
	if options.EnableGeoLocation || options.EnableTLSSecurity ||
		options.EnableFingerprint || options.EnableScreenshot {
		utils.Logger.Info("执行高级分析...")

		// 地理位置信息
		if options.EnableGeoLocation {
			utils.Logger.Debug("获取地理位置信息...")
			ipToCheck := host
			if !isIP && len(aRecords) > 0 {
				ipToCheck = aRecords[0]
			}

			// 直接使用IP-API.com查询地理位置信息，不需要本地数据库

			if geoLocation, err := utils.GetIPGeolocation(ipToCheck); err == nil {
				response.AdvancedAnalysis.GeoLocation = &models.GeoLocationInfo{
					Country:     geoLocation.Country,
					CountryCode: geoLocation.CountryCode,
					Region:      geoLocation.Region,
					City:        geoLocation.City,
					PostalCode:  geoLocation.PostalCode,
					Latitude:    geoLocation.Latitude,
					Longitude:   geoLocation.Longitude,
					Timezone:    geoLocation.Timezone,
				}
				utils.Logger.Info("获取到地理位置信息: %s, %s", geoLocation.City, geoLocation.Country)
			} else {
				utils.Logger.Warn("获取地理位置信息失败: %v", err)
			}
		}

		// TLS安全评估
		if options.EnableTLSSecurity {
			utils.Logger.Debug("执行TLS安全评估...")

			// 使用增强的TLS检测
			enhancedTLSResult, err := utils.EnhancedTLSCheck(host, portNum)
			if err == nil || enhancedTLSResult.ConnectionSuccessful {
				// 转换TLS版本
				var versions []string
				for _, v := range enhancedTLSResult.SupportedVersions {
					versions = append(versions, string(v))
				}

				// 转换密码套件
				var cipherSuites []models.CipherSuiteInfo
				for _, cs := range enhancedTLSResult.CipherSuites {
					cipherSuites = append(cipherSuites, models.CipherSuiteInfo{
						Name:     cs.Name,
						Strength: cs.Strength,
					})
				}

				// 转换漏洞
				vulnerabilities := make(map[string]bool)
				for vuln, vulnerable := range enhancedTLSResult.Vulnerabilities {
					vulnerabilities[string(vuln)] = vulnerable
				}

				response.AdvancedAnalysis.TLSSecurity = &models.TLSSecurityInfo{
					SupportedVersions:    versions,
					CertificateInfo:      enhancedTLSResult.CertificateInfo,
					CipherSuites:         cipherSuites,
					Vulnerabilities:      vulnerabilities,
					SecurityRating:       enhancedTLSResult.SecurityRating,
					Recommendations:      enhancedTLSResult.Recommendations,
					ConnectionSuccessful: enhancedTLSResult.ConnectionSuccessful,
					ErrorMessage:         enhancedTLSResult.ErrorMessage,
					UsedMethod:           enhancedTLSResult.UsedMethod,
					TLSExtensions:        enhancedTLSResult.TLSExtensions,
					ALPN:                 enhancedTLSResult.ALPN,
					OCSP:                 enhancedTLSResult.OCSP,
					CT:                   enhancedTLSResult.CT,
					SessionTickets:       enhancedTLSResult.SessionTickets,
					SNI:                  enhancedTLSResult.SNI,
					RetryCount:           enhancedTLSResult.RetryCount,
					TotalTime:            enhancedTLSResult.TotalTime,
				}

				if enhancedTLSResult.ConnectionSuccessful {
					utils.Logger.Info("TLS安全评级: %s (使用方法: %s, 重试次数: %d, 耗时: %dms)",
						enhancedTLSResult.SecurityRating,
						enhancedTLSResult.UsedMethod,
						enhancedTLSResult.RetryCount,
						enhancedTLSResult.TotalTime)
				} else {
					utils.Logger.Warn("TLS连接失败: %s (重试次数: %d, 耗时: %dms)",
						enhancedTLSResult.ErrorMessage,
						enhancedTLSResult.RetryCount,
						enhancedTLSResult.TotalTime)
				}
			} else {
				// 如果增强检测完全失败，尝试使用基本检测
				utils.Logger.Warn("增强TLS检测失败: %v，尝试基本检测", err)

				if tlsResult, err := utils.CheckTLSSecurity(host, portNum); err == nil {
					// 转换TLS版本
					var versions []string
					for _, v := range tlsResult.SupportedVersions {
						versions = append(versions, string(v))
					}

					// 转换密码套件
					var cipherSuites []models.CipherSuiteInfo
					for _, cs := range tlsResult.CipherSuites {
						cipherSuites = append(cipherSuites, models.CipherSuiteInfo{
							Name:     cs.Name,
							Strength: cs.Strength,
						})
					}

					// 转换漏洞
					vulnerabilities := make(map[string]bool)
					for vuln, vulnerable := range tlsResult.Vulnerabilities {
						vulnerabilities[string(vuln)] = vulnerable
					}

					response.AdvancedAnalysis.TLSSecurity = &models.TLSSecurityInfo{
						SupportedVersions:    versions,
						CertificateInfo:      tlsResult.CertificateInfo,
						CipherSuites:         cipherSuites,
						Vulnerabilities:      vulnerabilities,
						SecurityRating:       tlsResult.SecurityRating,
						Recommendations:      tlsResult.Recommendations,
						ConnectionSuccessful: true,
					}
					utils.Logger.Info("TLS安全评级: %s", tlsResult.SecurityRating)
				} else {
					utils.Logger.Warn("TLS安全评估失败: %v", err)

					// 即使失败也创建TLS信息对象，记录错误信息
					response.AdvancedAnalysis.TLSSecurity = &models.TLSSecurityInfo{
						ConnectionSuccessful: false,
						ErrorMessage:         err.Error(),
					}
				}
			}
		}

		// 网站指纹识别
		if options.EnableFingerprint {
			utils.Logger.Debug("执行网站指纹识别...")

			// 尝试使用增强的网站指纹识别
			enhancedFingerprint, err := utils.EnhancedWebFingerprintDetection(inputURL)
			if err == nil {
				response.AdvancedAnalysis.WebFingerprint = &models.WebFingerprintInfo{
					ServerType:           enhancedFingerprint.ServerType,
					ServerVersion:        enhancedFingerprint.ServerVersion,
					CMS:                  enhancedFingerprint.CMS,
					CMSVersion:           enhancedFingerprint.CMSVersion,
					Frameworks:           enhancedFingerprint.Frameworks,
					JavaScript:           enhancedFingerprint.JavaScript,
					Analytics:            enhancedFingerprint.Analytics,
					Technologies:         enhancedFingerprint.Technologies,
					Confidence:           enhancedFingerprint.Confidence,
					FrameworkVersions:    enhancedFingerprint.FrameworkVersions,
					ProgrammingLanguages: enhancedFingerprint.ProgrammingLanguages,
					DatabaseTechnologies: enhancedFingerprint.DatabaseTechnologies,
					SecurityFeatures:     enhancedFingerprint.SecurityFeatures,
					ContentHash:          enhancedFingerprint.ContentHash,
					ResponseTime:         enhancedFingerprint.ResponseTime,
					ResponseSize:         enhancedFingerprint.ResponseSize,
					HeaderFingerprints:   enhancedFingerprint.HeaderFingerprints,
					CookieFingerprints:   enhancedFingerprint.CookieFingerprints,
				}

				// 记录更详细的信息
				utils.Logger.Info("识别到网站技术: 服务器=%s, CMS=%s", enhancedFingerprint.ServerType, enhancedFingerprint.CMS)
				if len(enhancedFingerprint.ProgrammingLanguages) > 0 {
					utils.Logger.Info("识别到编程语言: %s", strings.Join(enhancedFingerprint.ProgrammingLanguages, ", "))
				}
				if len(enhancedFingerprint.DatabaseTechnologies) > 0 {
					utils.Logger.Info("识别到数据库技术: %s", strings.Join(enhancedFingerprint.DatabaseTechnologies, ", "))
				}
			} else {
				// 如果增强识别失败，回退到基本识别
				utils.Logger.Warn("增强网站指纹识别失败: %v，尝试基本识别", err)

				if fingerprint, err := utils.DetectWebFingerprint(inputURL); err == nil {
					response.AdvancedAnalysis.WebFingerprint = &models.WebFingerprintInfo{
						ServerType:    fingerprint.ServerType,
						ServerVersion: fingerprint.ServerVersion,
						CMS:           fingerprint.CMS,
						CMSVersion:    fingerprint.CMSVersion,
						Frameworks:    fingerprint.Frameworks,
						JavaScript:    fingerprint.JavaScript,
						Analytics:     fingerprint.Analytics,
						Technologies:  fingerprint.Technologies,
						Confidence:    fingerprint.Confidence,
					}
					utils.Logger.Info("识别到网站技术: 服务器=%s, CMS=%s", fingerprint.ServerType, fingerprint.CMS)
				} else {
					utils.Logger.Warn("网站指纹识别失败: %v", err)
				}
			}
		}

		// 网站截图
		if options.EnableScreenshot {
			utils.Logger.Debug("执行网站截图...")

			// 设置截图选项
			screenshotOptions := utils.DefaultScreenshotOptions()
			if options.ScreenshotFormat != "" {
				if strings.ToLower(options.ScreenshotFormat) == "jpeg" {
					screenshotOptions.Format = utils.FormatJPEG
				} else {
					screenshotOptions.Format = utils.FormatPNG
				}
			}

			if options.ScreenshotWidth > 0 {
				screenshotOptions.Width = options.ScreenshotWidth
			}

			if options.ScreenshotHeight > 0 {
				screenshotOptions.Height = options.ScreenshotHeight
			}

			screenshotOptions.FullPage = options.ScreenshotFullPage

			if screenshotPath, err := utils.CaptureScreenshot(inputURL, screenshotOptions); err == nil {
				// 获取文件大小
				fileInfo, err := os.Stat(screenshotPath)
				fileSize := int64(0)
				if err == nil {
					fileSize = fileInfo.Size()
				}

				response.AdvancedAnalysis.Screenshot = &models.ScreenshotInfo{
					FilePath: screenshotPath,
					Format:   string(screenshotOptions.Format),
					Width:    screenshotOptions.Width,
					Height:   screenshotOptions.Height,
					FileSize: fileSize,
				}
				utils.Logger.Info("已保存网站截图: %s", screenshotPath)
			} else {
				utils.Logger.Warn("网站截图失败: %v", err)
			}
		}
	}

	return response, nil
}

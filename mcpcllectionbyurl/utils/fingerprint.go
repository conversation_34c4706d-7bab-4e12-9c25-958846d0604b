package utils

import (
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"strings"
	"time"
)

// WebFingerprint 网站指纹信息
type WebFingerprint struct {
	ServerType    string            // Web服务器类型
	ServerVersion string            // Web服务器版本
	CMS           string            // 内容管理系统
	CMSVersion    string            // CMS版本
	Frameworks    []string          // 使用的框架
	JavaScript    []string          // 使用的JavaScript库
	Analytics     []string          // 分析工具
	Technologies  []string          // 其他技术
	Headers       map[string]string // HTTP头信息
	Cookies       map[string]string // Cookie信息
	Confidence    int               // 置信度（0-100）
}

// DetectWebFingerprint 检测网站指纹
func DetectWebFingerprint(urlStr string) (*WebFingerprint, error) {

	// 确保URL格式正确
	if !strings.HasPrefix(urlStr, "http://") && !strings.HasPrefix(urlStr, "https://") {
		// 尝试HTTP
		httpURL := "http://" + urlStr
		httpFingerprint, httpErr := tryDetectFingerprint(httpURL)
		if httpErr == nil {
			return httpFingerprint, nil
		}

		// 如果HTTP失败，尝试HTTPS
		httpsURL := "https://" + urlStr
		httpsFingerprint, httpsErr := tryDetectFingerprint(httpsURL)
		if httpsErr == nil {
			return httpsFingerprint, nil
		}

		// 两者都失败，返回HTTP错误
		return nil, fmt.Errorf("HTTP和HTTPS请求都失败: %v, %v", httpErr, httpsErr)
	}

	// URL已经包含协议
	return tryDetectFingerprint(urlStr)
}

// tryDetectFingerprint 尝试检测指定URL的网站指纹
func tryDetectFingerprint(url string) (*WebFingerprint, error) {
	// 初始化结果
	fingerprint := &WebFingerprint{
		Frameworks:   []string{},
		JavaScript:   []string{},
		Analytics:    []string{},
		Technologies: []string{},
		Headers:      make(map[string]string),
		Cookies:      make(map[string]string),
		Confidence:   0,
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 10 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 最多允许5次重定向
			if len(via) >= 5 {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 如果启用了代理，使用代理
	if IsProxyEnabled() {
		client.Transport = GetProxyTransport()
	}

	// 创建请求
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	// 发送请求
	Logger.Debug("发送HTTP请求到 %s", url)
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %v", err)
	}

	// 收集HTTP头信息
	for name, values := range resp.Header {
		fingerprint.Headers[strings.ToLower(name)] = strings.Join(values, ", ")
	}

	// 收集Cookie信息
	for _, cookie := range resp.Cookies() {
		fingerprint.Cookies[cookie.Name] = cookie.Value
	}

	// 分析服务器类型
	if server, ok := fingerprint.Headers["server"]; ok {
		fingerprint.ServerType = extractServerType(server)
		fingerprint.ServerVersion = extractServerVersion(server)
	}

	// 分析页面内容
	bodyStr := string(body)

	// 检测CMS
	fingerprint.CMS, fingerprint.CMSVersion = detectCMS(bodyStr, fingerprint.Headers)

	// 检测JavaScript框架
	fingerprint.Frameworks = detectFrameworks(bodyStr)

	// 检测JavaScript库
	fingerprint.JavaScript = detectJavaScriptLibraries(bodyStr)

	// 检测分析工具
	fingerprint.Analytics = detectAnalytics(bodyStr)

	// 检测其他技术
	fingerprint.Technologies = detectTechnologies(bodyStr, fingerprint.Headers)

	// 计算置信度
	fingerprint.Confidence = calculateConfidence(fingerprint)

	return fingerprint, nil
}

// extractServerType 提取服务器类型
func extractServerType(serverHeader string) string {
	// 提取服务器类型（第一个单词）
	parts := strings.Split(serverHeader, "/")
	if len(parts) > 0 {
		return strings.TrimSpace(parts[0])
	}
	return serverHeader
}

// extractServerVersion 提取服务器版本
func extractServerVersion(serverHeader string) string {
	// 提取服务器版本（第一个斜杠后面的部分）
	parts := strings.Split(serverHeader, "/")
	if len(parts) > 1 {
		return strings.TrimSpace(parts[1])
	}
	return ""
}

// detectCMS 检测CMS
func detectCMS(body string, headers map[string]string) (string, string) {
	// WordPress
	if strings.Contains(body, "wp-content") || strings.Contains(body, "wp-includes") {
		version := extractRegexGroup(body, `meta name="generator" content="WordPress ([0-9.]+)"`)
		return "WordPress", version
	}

	// Joomla
	if strings.Contains(body, "joomla") || strings.Contains(body, "/media/jui/") {
		version := extractRegexGroup(body, `meta name="generator" content="Joomla! ([0-9.]+)"`)
		return "Joomla", version
	}

	// Drupal
	if strings.Contains(body, "drupal") || strings.Contains(body, "Drupal.settings") {
		version := extractRegexGroup(body, `"Drupal ([0-9.]+)"`)
		return "Drupal", version
	}

	// Magento
	if strings.Contains(body, "Mage.Cookies") || strings.Contains(body, "magento") {
		version := extractRegexGroup(body, `Magento/([0-9.]+)`)
		return "Magento", version
	}

	// TYPO3
	if strings.Contains(body, "TYPO3") || strings.Contains(body, "typo3temp") {
		version := extractRegexGroup(body, `TYPO3 ([0-9.]+)`)
		return "TYPO3", version
	}

	return "", ""
}

// detectFrameworks 检测框架
func detectFrameworks(body string) []string {
	var frameworks []string

	// Bootstrap
	if strings.Contains(body, "bootstrap.css") || strings.Contains(body, "bootstrap.min.css") {
		frameworks = append(frameworks, "Bootstrap")
	}

	// React
	if strings.Contains(body, "react.js") || strings.Contains(body, "react.min.js") ||
		strings.Contains(body, "react-dom") || strings.Contains(body, "_reactRootContainer") {
		frameworks = append(frameworks, "React")
	}

	// Angular
	if strings.Contains(body, "angular.js") || strings.Contains(body, "angular.min.js") ||
		strings.Contains(body, "ng-app") || strings.Contains(body, "ng-controller") {
		frameworks = append(frameworks, "Angular")
	}

	// Vue.js
	if strings.Contains(body, "vue.js") || strings.Contains(body, "vue.min.js") ||
		strings.Contains(body, "__vue__") {
		frameworks = append(frameworks, "Vue.js")
	}

	return frameworks
}

// detectJavaScriptLibraries 检测JavaScript库
func detectJavaScriptLibraries(body string) []string {
	var libraries []string

	// jQuery
	if strings.Contains(body, "jquery") || strings.Contains(body, "jQuery") {
		libraries = append(libraries, "jQuery")
	}

	// Lodash
	if strings.Contains(body, "lodash") || strings.Contains(body, "_.VERSION") {
		libraries = append(libraries, "Lodash")
	}

	// Moment.js
	if strings.Contains(body, "moment.js") || strings.Contains(body, "moment.min.js") {
		libraries = append(libraries, "Moment.js")
	}

	// Axios
	if strings.Contains(body, "axios.min.js") || strings.Contains(body, "axios.js") {
		libraries = append(libraries, "Axios")
	}

	return libraries
}

// detectAnalytics 检测分析工具
func detectAnalytics(body string) []string {
	var analytics []string

	// Google Analytics
	if strings.Contains(body, "google-analytics.com") || strings.Contains(body, "ga('create'") ||
		strings.Contains(body, "gtag(") {
		analytics = append(analytics, "Google Analytics")
	}

	// Google Tag Manager
	if strings.Contains(body, "googletagmanager.com") || strings.Contains(body, "gtm.js") {
		analytics = append(analytics, "Google Tag Manager")
	}

	// Matomo/Piwik
	if strings.Contains(body, "piwik.js") || strings.Contains(body, "matomo.js") ||
		strings.Contains(body, "_paq.push") {
		analytics = append(analytics, "Matomo/Piwik")
	}

	return analytics
}

// detectTechnologies 检测其他技术
func detectTechnologies(body string, headers map[string]string) []string {
	var technologies []string

	// PHP
	if poweredBy, ok := headers["x-powered-by"]; ok && strings.Contains(poweredBy, "PHP") {
		technologies = append(technologies, "PHP")
	}

	// ASP.NET
	if aspnetVersion, ok := headers["x-aspnet-version"]; ok {
		technologies = append(technologies, "ASP.NET "+aspnetVersion)
	} else if strings.Contains(body, "asp.net") || strings.Contains(body, "__VIEWSTATE") {
		technologies = append(technologies, "ASP.NET")
	}

	// Java
	if poweredBy, ok := headers["x-powered-by"]; ok &&
		(strings.Contains(poweredBy, "JSP") || strings.Contains(poweredBy, "Servlet")) {
		technologies = append(technologies, "Java")
	}

	// Ruby on Rails
	if poweredBy, ok := headers["x-powered-by"]; ok &&
		(strings.Contains(poweredBy, "Ruby") || strings.Contains(poweredBy, "Rails")) {
		technologies = append(technologies, "Ruby on Rails")
	}

	return technologies
}

// calculateConfidence 计算置信度
func calculateConfidence(fingerprint *WebFingerprint) int {
	confidence := 0

	// 服务器信息增加置信度
	if fingerprint.ServerType != "" {
		confidence += 20
		if fingerprint.ServerVersion != "" {
			confidence += 10
		}
	}

	// CMS信息增加置信度
	if fingerprint.CMS != "" {
		confidence += 20
		if fingerprint.CMSVersion != "" {
			confidence += 10
		}
	}

	// 框架信息增加置信度
	confidence += len(fingerprint.Frameworks) * 5

	// JavaScript库信息增加置信度
	confidence += len(fingerprint.JavaScript) * 3

	// 分析工具信息增加置信度
	confidence += len(fingerprint.Analytics) * 3

	// 其他技术信息增加置信度
	confidence += len(fingerprint.Technologies) * 5

	// 限制最大置信度为100
	if confidence > 100 {
		confidence = 100
	}

	return confidence
}

// extractRegexGroup 提取正则表达式组
func extractRegexGroup(text, pattern string) string {
	re := regexp.MustCompile(pattern)
	matches := re.FindStringSubmatch(text)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

package utils

import (
	"fmt"
	"log"
	"os"
	"time"
)

var (
	// Logger is the global logger instance
	Logger *MCPLogger
)

// LogLevel represents the severity of a log message
type LogLevel int

const (
	// DEBUG level for detailed troubleshooting
	DEBUG LogLevel = iota
	// INFO level for general operational information
	INFO
	// WARN level for warning conditions
	WARN
	// ERROR level for error conditions
	ERROR
	// FATAL level for critical errors that cause program termination
	FATAL
)

// MCPLogger is a custom logger for the MCP application
type MCPLogger struct {
	level  LogLevel
	logger *log.Logger
}

// init initializes the global logger
func init() {
	Logger = NewLogger(INFO)
}

// NewLogger creates a new MCPLogger with the specified log level
func NewLogger(level LogLevel) *MCPLogger {
	return &MCPLogger{
		level:  level,
		logger: log.New(os.Stdout, "", 0),
	}
}

// SetLevel sets the log level
func (l *MCPLogger) SetLevel(level LogLevel) {
	l.level = level
}

// log logs a message with the specified level
func (l *MCPLogger) log(level LogLevel, format string, args ...interface{}) {
	if level < l.level {
		return
	}

	var levelStr string
	switch level {
	case DEBUG:
		levelStr = "DEBUG"
	case INFO:
		levelStr = "INFO"
	case WARN:
		levelStr = "WARN"
	case ERROR:
		levelStr = "ERROR"
	case FATAL:
		levelStr = "FATAL"
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	message := fmt.Sprintf(format, args...)
	l.logger.Printf("[%s] %s: %s", timestamp, levelStr, message)

	if level == FATAL {
		os.Exit(1)
	}
}

// Debug logs a debug message
func (l *MCPLogger) Debug(format string, args ...interface{}) {
	l.log(DEBUG, format, args...)
}

// Info logs an info message
func (l *MCPLogger) Info(format string, args ...interface{}) {
	l.log(INFO, format, args...)
}

// Warn logs a warning message
func (l *MCPLogger) Warn(format string, args ...interface{}) {
	l.log(WARN, format, args...)
}

// Error logs an error message
func (l *MCPLogger) Error(format string, args ...interface{}) {
	l.log(ERROR, format, args...)
}

// Fatal logs a fatal message and exits the program
func (l *MCPLogger) Fatal(format string, args ...interface{}) {
	l.log(FATAL, format, args...)
}

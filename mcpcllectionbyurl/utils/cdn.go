package utils

import (
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"
)

// CDNInfo CDN信息
type CDNInfo struct {
	Provider   string            // CDN提供商
	Confidence int               // 置信度
	Headers    map[string]string // CDN相关的HTTP头
	CNames     []string          // CNAME记录
}

// FetchCDNHeaders 获取CDN相关的HTTP头
func FetchCDNHeaders(domain string) (cdn map[string]string, raw map[string]string) {
	cdn = make(map[string]string)
	raw = make(map[string]string)

	// 创建一个跳过证书验证的传输层
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // 跳过证书验证
		},
		DisableKeepAlives: true,
		IdleConnTimeout:   5 * time.Second,
	}

	client := &http.Client{
		Timeout:   15 * time.Second,
		Transport: tr,
	}

	// 首先尝试HTTPS
	u := fmt.Sprintf("https://%s", domain)
	req, _ := http.NewRequest(http.MethodHead, u, nil)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := client.Do(req)
	if err != nil {
		// HTTPS失败，尝试HTTP
		Logger.Debug("HTTPS请求失败: %v，尝试HTTP", err)
		u = fmt.Sprintf("http://%s", domain)
		req, _ = http.NewRequest(http.MethodHead, u, nil)
		req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

		resp, err = client.Do(req)
		if err != nil {
			cdn["error"] = err.Error()
			return
		}
	}

	defer resp.Body.Close()

	// 扩展检测的HTTP头
	pick := map[string]struct{}{
		"cf-ray":                 {},
		"cf-cache-status":        {},
		"x-cache":                {},
		"server":                 {},
		"via":                    {},
		"x-served-by":            {},
		"x-cache-hits":           {},
		"x-cdn":                  {},
		"x-amz-cf-id":            {},
		"x-azure-ref":            {},
		"x-varnish":              {},
		"x-fastly-request-id":    {},
		"x-akamai-transformed":   {},
		"x-iinfo":                {},
		"x-sucuri-id":            {},
		"x-swift-cachestatus":    {},
		"x-daa-tunnel":           {},
		"x-bce-cdn":              {},
		"x-ec-cache":             {},
		"x-ll-routing":           {},
		"x-sp-edge":              {},
		"x-cache-status":         {},
		"x-edge-location":        {},
		"x-powered-by":           {},
		"x-generator":            {},
		"x-drupal-cache":         {},
		"x-magento-cache":        {},
		"x-shopify-stage":        {},
		"x-wix-request-id":       {},
		"x-wordpress-cache":      {},
		"x-content-type-options": {},
		"x-xss-protection":       {},
		"x-frame-options":        {},
	}

	// 获取所有HTTP头
	for k, v := range resp.Header {
		key := strings.ToLower(k)
		raw[key] = strings.Join(v, "; ")
		if _, ok := pick[key]; ok {
			cdn[key] = raw[key]
		}
	}

	return
}

// GetCDNInfo 获取CDN信息
func GetCDNInfo(domain string) *CDNInfo {
	cdnInfo := &CDNInfo{
		Provider:   "",
		Confidence: 0,
		Headers:    make(map[string]string),
		CNames:     []string{},
	}

	// 获取CDN相关的HTTP头
	cdnHeaders, rawHeaders := FetchCDNHeaders(domain)
	cdnInfo.Headers = cdnHeaders

	// 如果HTTP请求失败，直接返回
	if _, ok := cdnHeaders["error"]; ok {
		return cdnInfo
	}

	// 获取CNAME记录
	cnames, err := lookupCNAME(domain)
	if err == nil {
		cdnInfo.CNames = cnames
	}

	// 检测CDN提供商
	provider, confidence := DetectCDNProvider(rawHeaders, cdnInfo.CNames)
	cdnInfo.Provider = provider
	cdnInfo.Confidence = confidence

	return cdnInfo
}

// lookupCNAME 查询域名的CNAME记录
func lookupCNAME(domain string) ([]string, error) {
	var cnames []string

	// 尝试直接获取CNAME
	cname, err := net.LookupCNAME(domain)
	if err == nil && cname != "" && cname != domain+"." {
		cnames = append(cnames, strings.TrimSuffix(cname, "."))
	}

	// 如果没有直接的CNAME，尝试通过NS记录查询
	if len(cnames) == 0 {
		nss, err := net.LookupNS(domain)
		if err == nil && len(nss) > 0 {
			for _, ns := range nss {
				cname := strings.TrimSuffix(ns.Host, ".")
				if cname != domain {
					cnames = append(cnames, cname)
				}
			}
		}
	}

	return cnames, nil
}

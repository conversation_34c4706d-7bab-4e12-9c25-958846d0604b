package utils

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"os/exec"
	"regexp"
	"strings"
	"time"
)

// TLSVersion TLS版本
type TLSVersion string

const (
	// TLSv10 TLS 1.0
	TLSv10 TLSVersion = "TLS 1.0"
	// TLSv11 TLS 1.1
	TLSv11 TLSVersion = "TLS 1.1"
	// TLSv12 TLS 1.2
	TLSv12 TLSVersion = "TLS 1.2"
	// TLSv13 TLS 1.3
	TLSv13 TLSVersion = "TLS 1.3"
	// SSLv3 SSL 3.0
	SSLv3 TLSVersion = "SSL 3.0"
)

// TLSVulnerability TLS漏洞
type TLSVulnerability string

const (
	// Heartbleed Heartbleed漏洞
	Heartbleed TLSVulnerability = "Heartbleed"
	// POODLE POODLE漏洞
	POODLE TLSVulnerability = "POODLE"
	// BEAST BEAST漏洞
	BEAST TLSVulnerability = "BEAST"
)

// TLSCipherSuite TLS密码套件
type TLSCipherSuite struct {
	Name     string // 密码套件名称
	Strength string // 强度评级（高/中/低）
}

// TLSSecurityResult TLS安全评估结果
type TLSSecurityResult struct {
	Host                string                       // 主机名
	Port                int                          // 端口
	SupportedVersions   []TLSVersion                 // 支持的TLS版本
	CertificateInfo     map[string]string            // 证书信息
	CipherSuites        []TLSCipherSuite             // 支持的密码套件
	Vulnerabilities     map[TLSVulnerability]bool    // 检测到的漏洞
	SecurityRating      string                       // 安全评级（A+/A/B/C/D/F）
	Recommendations     []string                     // 安全建议
}

// CheckTLSSecurity 检查TLS安全性
func CheckTLSSecurity(host string, port int) (*TLSSecurityResult, error) {
	result := &TLSSecurityResult{
		Host:            host,
		Port:            port,
		CertificateInfo: make(map[string]string),
		Vulnerabilities: make(map[TLSVulnerability]bool),
	}

	// 检查TLS版本支持
	supportedVersions, err := checkTLSVersions(host, port)
	if err != nil {
		return nil, fmt.Errorf("检查TLS版本支持失败: %v", err)
	}
	result.SupportedVersions = supportedVersions

	// 获取证书信息
	certInfo, err := getCertificateInfo(host, port)
	if err != nil {
		return nil, fmt.Errorf("获取证书信息失败: %v", err)
	}
	result.CertificateInfo = certInfo

	// 检查支持的密码套件
	cipherSuites, err := checkCipherSuites(host, port)
	if err != nil {
		Logger.Warn("检查密码套件失败: %v", err)
	} else {
		result.CipherSuites = cipherSuites
	}

	// 检查常见TLS漏洞
	result.Vulnerabilities[Heartbleed] = checkHeartbleed(host, port)
	result.Vulnerabilities[POODLE] = checkPOODLE(host, port)
	result.Vulnerabilities[BEAST] = checkBEAST(host, port)

	// 评估安全等级
	result.SecurityRating = calculateSecurityRating(result)
	
	// 生成安全建议
	result.Recommendations = generateSecurityRecommendations(result)

	return result, nil
}

// checkTLSVersions 检查支持的TLS版本
func checkTLSVersions(host string, port int) ([]TLSVersion, error) {
	var supportedVersions []TLSVersion
	
	// 检查TLS 1.0
	if isTLSVersionSupported(host, port, tls.VersionTLS10) {
		supportedVersions = append(supportedVersions, TLSv10)
	}
	
	// 检查TLS 1.1
	if isTLSVersionSupported(host, port, tls.VersionTLS11) {
		supportedVersions = append(supportedVersions, TLSv11)
	}
	
	// 检查TLS 1.2
	if isTLSVersionSupported(host, port, tls.VersionTLS12) {
		supportedVersions = append(supportedVersions, TLSv12)
	}
	
	// 检查TLS 1.3
	if isTLSVersionSupported(host, port, tls.VersionTLS13) {
		supportedVersions = append(supportedVersions, TLSv13)
	}
	
	// 检查SSL 3.0（使用OpenSSL）
	if isSSLv3Supported(host, port) {
		supportedVersions = append(supportedVersions, SSLv3)
	}
	
	return supportedVersions, nil
}

// isTLSVersionSupported 检查是否支持指定的TLS版本
func isTLSVersionSupported(host string, port int, version uint16) bool {
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	config := &tls.Config{
		MinVersion: version,
		MaxVersion: version,
		// 跳过证书验证，我们只关心连接是否成功
		InsecureSkipVerify: true,
	}
	
	conn, err := tls.DialWithDialer(dialer, "tcp", fmt.Sprintf("%s:%d", host, port), config)
	if err != nil {
		return false
	}
	defer conn.Close()
	
	return true
}

// isSSLv3Supported 检查是否支持SSL 3.0
func isSSLv3Supported(host string, port int) bool {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	cmd := exec.CommandContext(ctx, "openssl", "s_client", "-connect", fmt.Sprintf("%s:%d", host, port), "-ssl3")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return false
	}
	
	// 检查输出中是否包含成功建立连接的信息
	return strings.Contains(string(output), "Protocol  : SSLv3")
}

// getCertificateInfo 获取证书信息
func getCertificateInfo(host string, port int) (map[string]string, error) {
	info := make(map[string]string)
	
	dialer := &net.Dialer{
		Timeout: 5 * time.Second,
	}
	
	conn, err := tls.DialWithDialer(dialer, "tcp", fmt.Sprintf("%s:%d", host, port), &tls.Config{
		InsecureSkipVerify: true,
	})
	if err != nil {
		return nil, err
	}
	defer conn.Close()
	
	// 获取证书链
	certs := conn.ConnectionState().PeerCertificates
	if len(certs) == 0 {
		return nil, fmt.Errorf("未找到证书")
	}
	
	// 获取主证书信息
	cert := certs[0]
	info["Subject"] = cert.Subject.String()
	info["Issuer"] = cert.Issuer.String()
	info["NotBefore"] = cert.NotBefore.Format("2006-01-02 15:04:05")
	info["NotAfter"] = cert.NotAfter.Format("2006-01-02 15:04:05")
	info["SerialNumber"] = cert.SerialNumber.String()
	info["Version"] = fmt.Sprintf("%d", cert.Version)
	
	// 检查证书是否过期
	now := time.Now()
	if now.Before(cert.NotBefore) {
		info["Status"] = "未生效"
	} else if now.After(cert.NotAfter) {
		info["Status"] = "已过期"
	} else {
		info["Status"] = "有效"
	}
	
	// 获取SAN扩展
	var sans []string
	for _, dnsName := range cert.DNSNames {
		sans = append(sans, dnsName)
	}
	info["SubjectAltName"] = strings.Join(sans, ", ")
	
	return info, nil
}

// checkCipherSuites 检查支持的密码套件
func checkCipherSuites(host string, port int) ([]TLSCipherSuite, error) {
	// 使用OpenSSL获取支持的密码套件
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	cmd := exec.CommandContext(ctx, "openssl", "s_client", "-connect", fmt.Sprintf("%s:%d", host, port), "-cipher", "ALL")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("获取密码套件失败: %v", err)
	}
	
	// 解析输出以获取密码套件
	cipherSuites := parseCipherSuites(string(output))
	
	return cipherSuites, nil
}

// parseCipherSuites 解析OpenSSL输出以获取密码套件
func parseCipherSuites(output string) []TLSCipherSuite {
	var cipherSuites []TLSCipherSuite
	
	// 查找"Cipher    :"行
	re := regexp.MustCompile(`Cipher\s+:\s+(.+)`)
	matches := re.FindStringSubmatch(output)
	
	if len(matches) > 1 {
		cipherName := strings.TrimSpace(matches[1])
		
		// 评估密码套件强度
		strength := "中"
		if strings.Contains(cipherName, "NULL") || strings.Contains(cipherName, "EXPORT") || 
		   strings.Contains(cipherName, "DES") || strings.Contains(cipherName, "RC4") {
			strength = "低"
		} else if strings.Contains(cipherName, "AES256") || strings.Contains(cipherName, "CHACHA20") {
			strength = "高"
		}
		
		cipherSuites = append(cipherSuites, TLSCipherSuite{
			Name:     cipherName,
			Strength: strength,
		})
	}
	
	return cipherSuites
}

// checkHeartbleed 检查Heartbleed漏洞
func checkHeartbleed(host string, port int) bool {
	// 使用OpenSSL检查Heartbleed漏洞
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	cmd := exec.CommandContext(ctx, "openssl", "s_client", "-connect", fmt.Sprintf("%s:%d", host, port), "-tlsextdebug")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return false
	}
	
	// 检查是否存在Heartbeat扩展
	return strings.Contains(string(output), "heartbeat")
}

// checkPOODLE 检查POODLE漏洞
func checkPOODLE(host string, port int) bool {
	// POODLE主要影响SSLv3
	return isSSLv3Supported(host, port)
}

// checkBEAST 检查BEAST漏洞
func checkBEAST(host string, port int) bool {
	// BEAST主要影响TLS 1.0
	return isTLSVersionSupported(host, port, tls.VersionTLS10)
}

// calculateSecurityRating 计算安全评级
func calculateSecurityRating(result *TLSSecurityResult) string {
	// 检查是否支持TLS 1.2或更高版本
	supportsModernTLS := false
	for _, version := range result.SupportedVersions {
		if version == TLSv12 || version == TLSv13 {
			supportsModernTLS = true
			break
		}
	}
	
	// 检查是否支持旧版本TLS/SSL
	supportsOldTLS := false
	for _, version := range result.SupportedVersions {
		if version == TLSv10 || version == TLSv11 || version == SSLv3 {
			supportsOldTLS = true
			break
		}
	}
	
	// 检查是否存在漏洞
	hasVulnerabilities := false
	for _, vulnerable := range result.Vulnerabilities {
		if vulnerable {
			hasVulnerabilities = true
			break
		}
	}
	
	// 检查密码套件强度
	hasWeakCiphers := false
	for _, cipher := range result.CipherSuites {
		if cipher.Strength == "低" {
			hasWeakCiphers = true
			break
		}
	}
	
	// 根据以上条件评估安全等级
	if !supportsModernTLS {
		return "F" // 不支持现代TLS版本
	} else if hasVulnerabilities && hasWeakCiphers {
		return "D" // 存在漏洞且使用弱密码套件
	} else if hasVulnerabilities || hasWeakCiphers {
		return "C" // 存在漏洞或使用弱密码套件
	} else if supportsOldTLS {
		return "B" // 支持旧版本TLS/SSL
	} else if len(result.SupportedVersions) == 1 && result.SupportedVersions[0] == TLSv13 {
		return "A+" // 仅支持TLS 1.3
	} else {
		return "A" // 支持TLS 1.2及以上，无漏洞，无弱密码套件
	}
}

// generateSecurityRecommendations 生成安全建议
func generateSecurityRecommendations(result *TLSSecurityResult) []string {
	var recommendations []string
	
	// 检查是否支持旧版本TLS/SSL
	for _, version := range result.SupportedVersions {
		if version == TLSv10 || version == TLSv11 || version == SSLv3 {
			recommendations = append(recommendations, fmt.Sprintf("禁用不安全的协议版本: %s", version))
		}
	}
	
	// 检查是否存在漏洞
	for vuln, vulnerable := range result.Vulnerabilities {
		if vulnerable {
			recommendations = append(recommendations, fmt.Sprintf("修复 %s 漏洞", vuln))
		}
	}
	
	// 检查密码套件强度
	for _, cipher := range result.CipherSuites {
		if cipher.Strength == "低" {
			recommendations = append(recommendations, fmt.Sprintf("禁用弱密码套件: %s", cipher.Name))
		}
	}
	
	// 检查证书状态
	if status, ok := result.CertificateInfo["Status"]; ok && status != "有效" {
		recommendations = append(recommendations, "更新SSL证书")
	}
	
	// 如果没有TLS 1.3支持，建议启用
	supportsTLS13 := false
	for _, version := range result.SupportedVersions {
		if version == TLSv13 {
			supportsTLS13 = true
			break
		}
	}
	if !supportsTLS13 {
		recommendations = append(recommendations, "启用TLS 1.3支持")
	}
	
	// 如果没有问题，添加一个积极的建议
	if len(recommendations) == 0 {
		recommendations = append(recommendations, "当前TLS配置良好，继续保持")
	}
	
	return recommendations
}

/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"time"
)

// Option defines the configuration options for network analysis
type Option func(*Config)

// Config contains all configuration options for network analysis
type Config struct {
	// Analysis options
	EnableGeoLocation  bool `json:"enable_geo_location"`
	EnableTLSSecurity  bool `json:"enable_tls_security"`
	EnableFingerprint  bool `json:"enable_fingerprint"`
	EnableScreenshot   bool `json:"enable_screenshot"`
	EnableThreatIntel  bool `json:"enable_threat_intel"`
	EnableDNSAnalysis  bool `json:"enable_dns_analysis"`
	EnableWHOISLookup  bool `json:"enable_whois_lookup"`
	EnableCDNDetection bool `json:"enable_cdn_detection"`

	// Screenshot options
	ScreenshotFormat   string `json:"screenshot_format"`   // png, jpeg
	ScreenshotWidth    int    `json:"screenshot_width"`
	ScreenshotHeight   int    `json:"screenshot_height"`
	ScreenshotFullPage bool   `json:"screenshot_full_page"`

	// Network options
	Timeout        time.Duration `json:"timeout"`
	RetryCount     int           `json:"retry_count"`
	UserAgent      string        `json:"user_agent"`
	FollowRedirect bool          `json:"follow_redirect"`

	// Proxy options
	ProxyURL      string `json:"proxy_url"`
	ProxyUsername string `json:"proxy_username"`
	ProxyPassword string `json:"proxy_password"`

	// Concurrency options (for batch processing)
	MaxConcurrency int `json:"max_concurrency"`
	BatchSize      int `json:"batch_size"`

	// Output options
	IncludeRawData bool              `json:"include_raw_data"`
	CustomFields   map[string]string `json:"custom_fields"`

	// Advanced options
	DNSServers     []string          `json:"dns_servers"`
	TLSVersions    []string          `json:"tls_versions"`
	CustomHeaders  map[string]string `json:"custom_headers"`
	VerifySSL      bool              `json:"verify_ssl"`
	CollectCookies bool              `json:"collect_cookies"`
}

// DefaultConfig returns the default configuration
func DefaultConfig() *Config {
	return &Config{
		EnableGeoLocation:  true,
		EnableTLSSecurity:  true,
		EnableFingerprint:  true,
		EnableScreenshot:   false,
		EnableThreatIntel:  false,
		EnableDNSAnalysis:  true,
		EnableWHOISLookup:  true,
		EnableCDNDetection: true,

		ScreenshotFormat:   "png",
		ScreenshotWidth:    1280,
		ScreenshotHeight:   800,
		ScreenshotFullPage: true,

		Timeout:        30 * time.Second,
		RetryCount:     3,
		UserAgent:      "Eino-NetworkAnalyzer/1.0",
		FollowRedirect: true,

		MaxConcurrency: 5,
		BatchSize:      10,

		IncludeRawData: false,
		VerifySSL:      true,
		CollectCookies: false,
	}
}

// WithGeoLocation enables or disables geolocation analysis
func WithGeoLocation(enabled bool) Option {
	return func(c *Config) {
		c.EnableGeoLocation = enabled
	}
}

// WithTLSSecurity enables or disables TLS security analysis
func WithTLSSecurity(enabled bool) Option {
	return func(c *Config) {
		c.EnableTLSSecurity = enabled
	}
}

// WithFingerprint enables or disables website fingerprinting
func WithFingerprint(enabled bool) Option {
	return func(c *Config) {
		c.EnableFingerprint = enabled
	}
}

// WithScreenshot enables screenshot capture with specified options
func WithScreenshot(enabled bool, format string, width, height int, fullPage bool) Option {
	return func(c *Config) {
		c.EnableScreenshot = enabled
		if enabled {
			c.ScreenshotFormat = format
			c.ScreenshotWidth = width
			c.ScreenshotHeight = height
			c.ScreenshotFullPage = fullPage
		}
	}
}

// WithTimeout sets the request timeout
func WithTimeout(timeout time.Duration) Option {
	return func(c *Config) {
		c.Timeout = timeout
	}
}

// WithRetry sets the retry count for failed requests
func WithRetry(count int) Option {
	return func(c *Config) {
		c.RetryCount = count
	}
}

// WithProxy sets proxy configuration
func WithProxy(url, username, password string) Option {
	return func(c *Config) {
		c.ProxyURL = url
		c.ProxyUsername = username
		c.ProxyPassword = password
	}
}

// WithConcurrency sets concurrency options for batch processing
func WithConcurrency(maxConcurrency, batchSize int) Option {
	return func(c *Config) {
		c.MaxConcurrency = maxConcurrency
		c.BatchSize = batchSize
	}
}

// WithUserAgent sets the User-Agent header
func WithUserAgent(userAgent string) Option {
	return func(c *Config) {
		c.UserAgent = userAgent
	}
}

// WithCustomHeaders sets custom HTTP headers
func WithCustomHeaders(headers map[string]string) Option {
	return func(c *Config) {
		c.CustomHeaders = headers
	}
}

// WithDNSServers sets custom DNS servers
func WithDNSServers(servers []string) Option {
	return func(c *Config) {
		c.DNSServers = servers
	}
}

// WithTLSVersions sets allowed TLS versions
func WithTLSVersions(versions []string) Option {
	return func(c *Config) {
		c.TLSVersions = versions
	}
}

// WithRawData enables or disables inclusion of raw data in results
func WithRawData(enabled bool) Option {
	return func(c *Config) {
		c.IncludeRawData = enabled
	}
}

// WithSSLVerification enables or disables SSL certificate verification
func WithSSLVerification(enabled bool) Option {
	return func(c *Config) {
		c.VerifySSL = enabled
	}
}

// WithThreatIntel enables or disables threat intelligence collection
func WithThreatIntel(enabled bool) Option {
	return func(c *Config) {
		c.EnableThreatIntel = enabled
	}
}

// WithCustomFields sets custom metadata fields
func WithCustomFields(fields map[string]string) Option {
	return func(c *Config) {
		c.CustomFields = fields
	}
}

// ApplyOptions applies the given options to a config
func ApplyOptions(config *Config, opts ...Option) {
	for _, opt := range opts {
		opt(config)
	}
}

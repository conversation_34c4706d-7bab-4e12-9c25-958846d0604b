package main

import (
	"encoding/json"
	"fmt"
	"mcpcllectionbyurl/handler"
	"mcpcllectionbyurl/models"
	"mcpcllectionbyurl/utils"
	"os"
	"strings"
)

// processBatchFile 从文件批量处理多个URL，支持并发和多种输出格式
func processBatchFile(batchFile, outputFile, outputFormat string, maxConcurrency int, mcpOptions handler.MCPOptions) {
	// 读取批处理文件
	content, err := os.ReadFile(batchFile)
	if err != nil {
		utils.Logger.Fatal("读取批处理文件失败: %v", err)
	}

	// 按行分割内容
	urlLines := strings.Split(string(content), "\n")
	var urls []string

	// 过滤有效URL
	for _, url := range urlLines {
		url = strings.TrimSpace(url)
		if url == "" || strings.HasPrefix(url, "#") {
			continue // 跳过空行和注释
		}
		urls = append(urls, url)
	}

	utils.Logger.Info("找到 %d 个URL需要处理", len(urls))

	// 设置并发数
	if len(urls) < maxConcurrency {
		maxConcurrency = len(urls)
	}

	utils.Logger.Debug("使用 %d 个并发协程", maxConcurrency)

	// 创建接收结果的通道
	resultChan := make(chan struct {
		url    string
		result *models.MCPResponse
		err    error
	}, len(urls))

	// 创建信号量通道以限制并发
	semaphore := make(chan struct{}, maxConcurrency)

	// 启动goroutine并发处理URL
	for _, url := range urls {
		go func(url string) {
			// 获取信号量槽位
			semaphore <- struct{}{}
			defer func() { <-semaphore }() // 完成后释放槽位

			utils.Logger.Info("正在处理URL: %s", url)
			result, err := handler.HandleMCPRequestWithOptions(url, mcpOptions)

			// 将结果发送到通道
			resultChan <- struct {
				url    string
				result *models.MCPResponse
				err    error
			}{url, result, err}
		}(url)
	}

	// 收集结果
	var mcpResults []*models.MCPResponse
	for i := 0; i < len(urls); i++ {
		res := <-resultChan

		if res.err != nil {
			utils.Logger.Error("处理URL %s 时出错: %v", res.url, res.err)
			continue
		}

		// 添加URL字段
		// 注意：这里我们直接修改结果对象，而不是转换为map
		res.result.Domain = res.url
		mcpResults = append(mcpResults, res.result)
	}

	utils.Logger.Info("成功处理了 %d 个URL", len(mcpResults))

	// 导出结果
	if len(mcpResults) > 0 {
		exportOptions := utils.ExportOptions{
			Format:     utils.ExportFormat(outputFormat),
			OutputFile: outputFile,
			Pretty:     true,
		}

		if outputFile == "" {
			// 如果没有指定输出文件，则输出到控制台
			if outputFormat == "csv" {
				utils.Logger.Warn("CSV格式需要指定输出文件，将使用JSON格式输出到控制台")
				// 转换为JSON并输出到控制台
				jsonData, err := json.MarshalIndent(mcpResults, "", "  ")
				if err != nil {
					utils.Logger.Fatal("转换结果为JSON时出错: %v", err)
				}
				fmt.Println(string(jsonData))
			} else {
				// JSON格式输出到控制台
				jsonData, err := json.MarshalIndent(mcpResults, "", "  ")
				if err != nil {
					utils.Logger.Fatal("转换结果为JSON时出错: %v", err)
				}
				fmt.Println(string(jsonData))
			}
		} else {
			// 导出到文件
			if err := utils.ExportResults(mcpResults, exportOptions); err != nil {
				utils.Logger.Fatal("导出结果时出错: %v", err)
			}
			utils.Logger.Info("结果已写入到 %s", outputFile)
		}
	} else {
		utils.Logger.Warn("没有成功处理的URL，不生成输出文件")
	}
}

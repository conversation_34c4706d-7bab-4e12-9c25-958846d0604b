package utils

import (
	"context"
	"fmt"
	"mcpcllectionbyurl/models"
	"os/exec"
	"regexp"
	"strings"
	"sync"
	"time"
)

// WhoisCache WHOIS缓存
type WhoisCache struct {
	cache     map[string]*WhoisCacheEntry
	mutex     sync.RWMutex
	ttl       time.Duration
	lastClean time.Time
}

// WhoisCacheEntry WHOIS缓存条目
type WhoisCacheEntry struct {
	Raw       string
	Company   *models.CompanyizedWhoisResq
	Success   bool
	Timestamp time.Time
}

var (
	// 全局WHOIS缓存实例
	whoisCache = NewWhoisCache(24 * time.Hour)
)

// NewWhoisCache 创建新的WHOIS缓存
func NewWhoisCache(ttl time.Duration) *WhoisCache {
	return &WhoisCache{
		cache:     make(map[string]*WhoisCacheEntry),
		ttl:       ttl,
		lastClean: time.Now(),
	}
}

// Get 从缓存获取WHOIS信息
func (c *WhoisCache) Get(domain string) (*WhoisCacheEntry, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	entry, ok := c.cache[domain]
	if !ok {
		return nil, false
	}

	// 检查条目是否过期
	if time.Since(entry.Timestamp) > c.ttl {
		return nil, false
	}

	return entry, true
}

// Set 将WHOIS信息添加到缓存
func (c *WhoisCache) Set(domain string, raw string, company *models.CompanyizedWhoisResq, success bool) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.cache[domain] = &WhoisCacheEntry{
		Raw:       raw,
		Company:   company,
		Success:   success,
		Timestamp: time.Now(),
	}

	// 定期清理过期条目
	if time.Since(c.lastClean) > time.Hour {
		c.cleanExpired()
		c.lastClean = time.Now()
	}
}

// cleanExpired 清理过期的缓存条目
func (c *WhoisCache) cleanExpired() {
	now := time.Now()
	for domain, entry := range c.cache {
		if now.Sub(entry.Timestamp) > c.ttl {
			delete(c.cache, domain)
		}
	}
}

// GetWhoisAndCompany 获取域名的WHOIS信息和公司信息
func GetWhoisAndCompany(domain string) (raw string, company *models.CompanyizedWhoisResq, success bool, runtimeMs int) {
	start := time.Now()

	// 检查缓存
	if entry, found := whoisCache.Get(domain); found {
		Logger.Debug("WHOIS缓存命中: %s", domain)
		return entry.Raw, entry.Company, entry.Success, 0
	}

	Logger.Debug("WHOIS缓存未命中: %s", domain)

	// 设置WHOIS命令超时
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 执行WHOIS命令
	cmd := exec.CommandContext(ctx, "whois", domain)
	out, err := cmd.Output()
	runtimeMs = int(time.Since(start).Milliseconds())

	if err != nil || len(out) == 0 {
		Logger.Warn("WHOIS查询失败: %v", err)
		success = false
		raw = string(out)

		// 缓存结果
		whoisCache.Set(domain, raw, nil, success)
		return raw, nil, success, runtimeMs
	}

	// WHOIS查询成功
	success = true
	raw = string(out)

	// 从WHOIS数据中提取公司信息
	company = extractCompanyInfo(raw)

	// 缓存结果
	whoisCache.Set(domain, raw, company, success)

	return raw, company, success, runtimeMs
}

// extractCompanyInfo 从WHOIS数据中提取公司信息
func extractCompanyInfo(whoisData string) *models.CompanyizedWhoisResq {
	// 初始化公司信息
	company := &models.CompanyizedWhoisResq{}

	// 尝试提取公司名称
	companyName := extractField(whoisData, "Registrant Organization")
	if companyName == "" {
		companyName = extractField(whoisData, "Organization")
	}
	if companyName == "" {
		companyName = extractField(whoisData, "org")
	}
	if companyName == "" {
		companyName = extractField(whoisData, "Registrant Name")
	}

	// 尝试提取联系人
	legalPerson := extractField(whoisData, "Admin Name")
	if legalPerson == "" {
		legalPerson = extractField(whoisData, "Registrant Name")
	}
	if legalPerson == "" {
		legalPerson = extractField(whoisData, "Technical Contact")
	}

	// 尝试提取状态
	status := extractField(whoisData, "Domain Status")
	if status == "" {
		status = extractField(whoisData, "Status")
	}

	// 尝试提取注册号
	regNumber := extractField(whoisData, "Registry Domain ID")
	if regNumber == "" {
		regNumber = extractField(whoisData, "Domain ID")
	}

	// 设置公司信息
	company.CompanyName = companyName
	company.LegalPerson = legalPerson
	company.Status = status
	company.RegistrationNumber = regNumber

	// 如果没有提取到任何信息，返回nil
	if companyName == "" && legalPerson == "" && status == "" && regNumber == "" {
		return nil
	}

	return company
}

// extractField 从WHOIS数据中提取字段值
func extractField(data, fieldName string) string {
	// 创建匹配模式
	pattern := fmt.Sprintf("(?i)%s[\\s]*:([^\n\r]*)", regexp.QuoteMeta(fieldName))
	re := regexp.MustCompile(pattern)

	// 查找匹配项
	matches := re.FindStringSubmatch(data)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	return ""
}

// GetIPWhoisInfo 获取IP地址的WHOIS信息
func GetIPWhoisInfo(ip string) (raw string, company *models.CompanyizedWhoisResq, success bool, runtimeMs int) {
	start := time.Now()

	// 检查缓存
	if entry, found := whoisCache.Get(ip); found {
		Logger.Debug("WHOIS缓存命中: %s", ip)
		return entry.Raw, entry.Company, entry.Success, 0
	}

	Logger.Debug("WHOIS缓存未命中: %s", ip)

	// 设置WHOIS命令超时
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 执行WHOIS命令，对IP地址查询
	cmd := exec.CommandContext(ctx, "whois", ip)
	out, err := cmd.Output()
	runtimeMs = int(time.Since(start).Milliseconds())

	if err != nil || len(out) == 0 {
		Logger.Warn("IP WHOIS查询失败: %v", err)
		success = false
		raw = string(out)

		// 缓存结果
		whoisCache.Set(ip, raw, nil, success)
		return raw, nil, success, runtimeMs
	}

	// WHOIS查询成功
	success = true
	raw = string(out)

	// 从WHOIS数据中提取组织信息
	company = extractIPOrgInfo(raw)

	// 缓存结果
	whoisCache.Set(ip, raw, company, success)

	return raw, company, success, runtimeMs
}

// extractIPOrgInfo 从IP WHOIS数据中提取组织信息
func extractIPOrgInfo(whoisData string) *models.CompanyizedWhoisResq {
	// 初始化公司信息
	company := &models.CompanyizedWhoisResq{}

	// 尝试提取组织名称
	companyName := extractField(whoisData, "Organization")
	if companyName == "" {
		companyName = extractField(whoisData, "org-name")
	}
	if companyName == "" {
		companyName = extractField(whoisData, "OrgName")
	}
	if companyName == "" {
		companyName = extractField(whoisData, "descr")
	}

	// 尝试提取联系人
	legalPerson := extractField(whoisData, "Admin Name")
	if legalPerson == "" {
		legalPerson = extractField(whoisData, "admin-c")
	}
	if legalPerson == "" {
		legalPerson = extractField(whoisData, "tech-c")
	}

	// 尝试提取状态
	status := extractField(whoisData, "status")
	if status == "" {
		status = extractField(whoisData, "Status")
	}

	// 尝试提取网络范围
	regNumber := extractField(whoisData, "NetRange")
	if regNumber == "" {
		regNumber = extractField(whoisData, "inetnum")
	}
	if regNumber == "" {
		regNumber = extractField(whoisData, "CIDR")
	}

	// 设置公司信息
	company.CompanyName = companyName
	company.LegalPerson = legalPerson
	company.Status = status
	company.RegistrationNumber = regNumber

	// 如果没有提取到任何信息，返回nil
	if companyName == "" && legalPerson == "" && status == "" && regNumber == "" {
		return nil
	}

	return company
}

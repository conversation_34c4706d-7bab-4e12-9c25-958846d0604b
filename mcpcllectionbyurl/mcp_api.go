package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os/exec"
	"strings"
)

// 响应结构
type ApiResponse struct {
	Success bool            `json:"success"`
	Message string          `json:"message,omitempty"`
	Data    json.RawMessage `json:"data,omitempty"`
	Error   string          `json:"error,omitempty"`
}

func main() {
	// 解析命令行参数
	port := flag.String("port", "8080", "API服务器端口")
	flag.Parse()

	// 设置路由
	http.HandleFunc("/api/analyze", analyzeHandler)
	http.HandleFunc("/api/health", healthCheckHandler)
	http.HandleFunc("/", homeHandler)

	// 启动服务器
	fmt.Printf("MCP API服务器启动在 http://0.0.0.0:%s\n", *port)
	log.Fatal(http.ListenAndServe(fmt.Sprintf(":%s", *port), nil))
}

// 主页处理函数
func homeHandler(w http.ResponseWriter, r *http.Request) {
	if r.URL.Path != "/" {
		http.NotFound(w, r)
		return
	}

	html := `
<!DOCTYPE html>
<html>
<head>
    <title>MCP API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .endpoint {
            margin-bottom: 30px;
        }
        .method {
            font-weight: bold;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <h1>MCP-1文档</h1>
    <p>欢迎使用MCP-1。此MCP允许您分析域名和URL，获取各种网络情报信息。</p>

    <div class="endpoint">
        <h2>分析域名或URL</h2>
        <p><span class="method">GET</span> /api/analyze</p>
        <h3>查询参数:</h3>
        <ul>
            <li><strong>url</strong> (必需): 要分析的URL或域名</li>
            <li><strong>tls_check</strong> (可选): 是否执行TLS安全评估 (true/false)</li>
            <li><strong>fingerprint</strong> (可选): 是否执行网站指纹识别 (true/false)</li>
            <li><strong>verbose</strong> (可选): 是否输出详细信息 (true/false)</li>
        </ul>
        <h3>示例:</h3>
        <pre>GET /api/analyze?url=www.example.com&tls_check=true&fingerprint=true</pre>
    </div>

    <div class="endpoint">
        <h2>健康检查</h2>
        <p><span class="method">GET</span> /api/health</p>
        <h3>示例:</h3>
        <pre>GET /api/health</pre>
    </div>
</body>
</html>
`
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// 健康检查处理函数
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
	response := ApiResponse{
		Success: true,
		Message: "MCP-1服务正常运行",
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 分析处理函数
func analyzeHandler(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头，允许跨域请求
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	// 处理OPTIONS请求
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 获取URL参数
	url := r.URL.Query().Get("url")
	if url == "" {
		sendErrorResponse(w, "缺少URL参数", http.StatusBadRequest)
		return
	}

	// 构建命令行参数
	args := []string{"-url=" + url, "-format=json"}

	// 添加可选参数
	if r.URL.Query().Get("tls_check") == "true" {
		args = append(args, "-tls-check=true")
	}

	if r.URL.Query().Get("fingerprint") == "true" {
		args = append(args, "-fingerprint=true")
	}

	if r.URL.Query().Get("verbose") == "true" {
		args = append(args, "-verbose")
	}

	// 执行MCP命令
	cmd := exec.Command("./mcpcllectionbyurl", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		sendErrorResponse(w, fmt.Sprintf("执行MCP失败: %v", err), http.StatusInternalServerError)
		return
	}

	// 解析JSON输出
	var result json.RawMessage
	if err := json.Unmarshal(output, &result); err != nil {
		sendErrorResponse(w, fmt.Sprintf("解析MCP输出失败: %v\n输出: %s", err, string(output)), http.StatusInternalServerError)
		return
	}

	// 发送成功响应
	response := ApiResponse{
		Success: true,
		Data:    result,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// 发送错误响应
func sendErrorResponse(w http.ResponseWriter, message string, statusCode int) {
	response := ApiResponse{
		Success: false,
		Error:   message,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// 获取环境变量，如果不存在则返回默认值
func getEnvOrDefault(key, defaultValue string) string {
	value, err := ioutil.ReadFile("/proc/1/environ")
	if err != nil {
		return defaultValue
	}

	for _, env := range strings.Split(string(value), "\x00") {
		if strings.HasPrefix(env, key+"=") {
			return strings.TrimPrefix(env, key+"=")
		}
	}

	return defaultValue
}

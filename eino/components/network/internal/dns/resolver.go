/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package dns

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// Resolver provides DNS resolution capabilities
type Resolver struct {
	servers []string
	timeout time.Duration
}

// NewResolver creates a new DNS resolver
func NewResolver(servers []string, timeout time.Duration) *Resolver {
	if len(servers) == 0 {
		servers = []string{"system"}
	}
	if timeout == 0 {
		timeout = 5 * time.Second
	}
	return &Resolver{
		servers: servers,
		timeout: timeout,
	}
}

// ResolveDomain resolves a domain name to IP addresses and collects DNS information
func (r *Resolver) ResolveDomain(ctx context.Context, domain string) (*schema.DNSInfo, error) {
	dnsInfo := &schema.DNSInfo{
		DNSServerUsed: "system",
		RawHeaders:    make(map[string]string),
	}

	// Resolve A records (IPv4)
	aRecords, err := r.lookupA(ctx, domain)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve A records: %w", err)
	}
	dnsInfo.ARecords = aRecords

	// Resolve CNAME records
	cnameRecords, err := r.lookupCNAME(ctx, domain)
	if err == nil {
		dnsInfo.CNAMERecords = cnameRecords
	}

	// Resolve MX records
	mxRecords, err := r.lookupMX(ctx, domain)
	if err == nil {
		dnsInfo.MXRecords = mxRecords
	}

	// Resolve TXT records
	txtRecords, err := r.lookupTXT(ctx, domain)
	if err == nil {
		dnsInfo.TXTRecords = txtRecords
	}

	return dnsInfo, nil
}

// lookupA resolves A records (IPv4 addresses)
func (r *Resolver) lookupA(ctx context.Context, domain string) ([]string, error) {
	ctx, cancel := context.WithTimeout(ctx, r.timeout)
	defer cancel()

	ips, err := net.DefaultResolver.LookupIPAddr(ctx, domain)
	if err != nil {
		return nil, err
	}

	var ipv4s []string
	for _, ip := range ips {
		if ip.IP.To4() != nil {
			ipv4s = append(ipv4s, ip.IP.String())
		}
	}

	return ipv4s, nil
}

// lookupCNAME resolves CNAME records
func (r *Resolver) lookupCNAME(ctx context.Context, domain string) ([]string, error) {
	ctx, cancel := context.WithTimeout(ctx, r.timeout)
	defer cancel()

	cname, err := net.DefaultResolver.LookupCNAME(ctx, domain)
	if err != nil {
		return nil, err
	}

	// Remove trailing dot if present
	cname = strings.TrimSuffix(cname, ".")
	
	if cname != domain {
		return []string{cname}, nil
	}

	return nil, nil
}

// lookupMX resolves MX records
func (r *Resolver) lookupMX(ctx context.Context, domain string) ([]string, error) {
	ctx, cancel := context.WithTimeout(ctx, r.timeout)
	defer cancel()

	mxRecords, err := net.DefaultResolver.LookupMX(ctx, domain)
	if err != nil {
		return nil, err
	}

	var mxs []string
	for _, mx := range mxRecords {
		mxs = append(mxs, fmt.Sprintf("%d %s", mx.Pref, strings.TrimSuffix(mx.Host, ".")))
	}

	return mxs, nil
}

// lookupTXT resolves TXT records
func (r *Resolver) lookupTXT(ctx context.Context, domain string) ([]string, error) {
	ctx, cancel := context.WithTimeout(ctx, r.timeout)
	defer cancel()

	txtRecords, err := net.DefaultResolver.LookupTXT(ctx, domain)
	if err != nil {
		return nil, err
	}

	return txtRecords, nil
}

// IsValidDomain checks if a string is a valid domain name
func IsValidDomain(domain string) bool {
	if len(domain) == 0 || len(domain) > 253 {
		return false
	}

	// Check if it's an IP address
	if net.ParseIP(domain) != nil {
		return false
	}

	// Basic domain validation
	parts := strings.Split(domain, ".")
	if len(parts) < 2 {
		return false
	}

	for _, part := range parts {
		if len(part) == 0 || len(part) > 63 {
			return false
		}
		// Check for valid characters (simplified)
		for _, char := range part {
			if !((char >= 'a' && char <= 'z') || 
				 (char >= 'A' && char <= 'Z') || 
				 (char >= '0' && char <= '9') || 
				 char == '-') {
				return false
			}
		}
		// Cannot start or end with hyphen
		if strings.HasPrefix(part, "-") || strings.HasSuffix(part, "-") {
			return false
		}
	}

	return true
}

// IsIPAddress checks if a string is a valid IP address
func IsIPAddress(addr string) bool {
	return net.ParseIP(addr) != nil
}

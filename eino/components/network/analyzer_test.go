/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package network

import (
	"context"
	"net/url"
	"strings"
	"testing"
	"time"
)

func TestNewAnalyzer(t *testing.T) {
	analyzer := NewAnalyzer()
	if analyzer == nil {
		t.Fatal("NewAnalyzer returned nil")
	}

	if analyzer.GetType() != "NetworkAnalyzer" {
		t.Errorf("Expected type 'NetworkAnalyzer', got '%s'", analyzer.GetType())
	}

	if analyzer.IsCallbacksEnabled() != false {
		t.Error("Expected callbacks to be disabled by default")
	}
}

func TestAnalyzerWithOptions(t *testing.T) {
	analyzer := NewAnalyzer(
		WithGeoLocation(false),
		WithTLSSecurity(false),
		WithTimeout(10*time.Second),
		WithConcurrency(3, 5),
	)

	if analyzer.config.EnableGeoLocation {
		t.Error("Expected geolocation to be disabled")
	}

	if analyzer.config.EnableTLSSecurity {
		t.Error("Expected TLS security to be disabled")
	}

	if analyzer.config.Timeout != 10*time.Second {
		t.Errorf("Expected timeout 10s, got %v", analyzer.config.Timeout)
	}

	if analyzer.config.MaxConcurrency != 3 {
		t.Errorf("Expected max concurrency 3, got %d", analyzer.config.MaxConcurrency)
	}
}

func TestAnalyze_BasicDomain(t *testing.T) {
	analyzer := NewAnalyzer(
		WithGeoLocation(false), // Disable to avoid external API calls in tests
		WithTLSSecurity(false),
		WithFingerprint(false),
	)

	ctx := context.Background()
	result, err := analyzer.Analyze(ctx, "example.com")

	if err != nil {
		t.Fatalf("Analyze failed: %v", err)
	}

	if result == nil {
		t.Fatal("Result is nil")
	}

	if result.Domain != "example.com" {
		t.Errorf("Expected domain 'example.com', got '%s'", result.Domain)
	}

	if result.IsIPAccess {
		t.Error("Expected IsIPAccess to be false for domain")
	}

	if result.DNS == nil {
		t.Error("Expected DNS info to be present")
	}

	if len(result.DNS.ARecords) == 0 {
		t.Error("Expected at least one A record")
	}

	if result.IP == "" {
		t.Error("Expected IP to be set")
	}
}

func TestAnalyze_IPAddress(t *testing.T) {
	analyzer := NewAnalyzer(
		WithGeoLocation(false),
		WithTLSSecurity(false),
		WithFingerprint(false),
	)

	ctx := context.Background()
	result, err := analyzer.Analyze(ctx, "*******")

	if err != nil {
		t.Fatalf("Analyze failed: %v", err)
	}

	if !result.IsIPAccess {
		t.Error("Expected IsIPAccess to be true for IP address")
	}

	if result.Domain != "*******" {
		t.Errorf("Expected domain '*******', got '%s'", result.Domain)
	}

	if result.IP != "*******" {
		t.Errorf("Expected IP '*******', got '%s'", result.IP)
	}
}

func TestAnalyze_WithHTTPSScheme(t *testing.T) {
	analyzer := NewAnalyzer(
		WithGeoLocation(false),
		WithTLSSecurity(false),
		WithFingerprint(false),
	)

	ctx := context.Background()
	result, err := analyzer.Analyze(ctx, "https://example.com")

	if err != nil {
		t.Fatalf("Analyze failed: %v", err)
	}

	if result.Domain != "example.com" {
		t.Errorf("Expected domain 'example.com', got '%s'", result.Domain)
	}

	if result.Port != 443 {
		t.Errorf("Expected port 443, got %d", result.Port)
	}
}

func TestBatchAnalyze(t *testing.T) {
	analyzer := NewAnalyzer(
		WithGeoLocation(false),
		WithTLSSecurity(false),
		WithFingerprint(false),
		WithConcurrency(2, 5),
	)

	ctx := context.Background()
	urls := []string{
		"example.com",
		"google.com",
		"*******",
	}

	results, err := analyzer.BatchAnalyze(ctx, urls)

	if err != nil {
		t.Fatalf("BatchAnalyze failed: %v", err)
	}

	if len(results) != len(urls) {
		t.Errorf("Expected %d results, got %d", len(urls), len(results))
	}

	for i, result := range results {
		if result == nil {
			t.Errorf("Result %d is nil", i)
			continue
		}

		expectedDomain := urls[i]
		if strings.HasPrefix(expectedDomain, "http") {
			u, _ := url.Parse(expectedDomain)
			expectedDomain = u.Hostname()
		}

		if result.Domain != expectedDomain {
			t.Errorf("Result %d: expected domain '%s', got '%s'", i, expectedDomain, result.Domain)
		}
	}
}

func TestAnalyze_InvalidURL(t *testing.T) {
	analyzer := NewAnalyzer()

	ctx := context.Background()
	_, err := analyzer.Analyze(ctx, "://invalid-url")

	if err == nil {
		t.Error("Expected error for invalid URL")
	}
}

package utils

import (
	"fmt"
	"net/http"
	"net/url"
	"sync"
)

// ProxyConfig 代理配置
type ProxyConfig struct {
	URL      string
	Username string // 用户名（可选）
	Password string // 密码（可选）
	Enabled  bool   // 是否启用代理
}

var (
	// 全局代理配置
	globalProxyConfig ProxyConfig
	proxyMutex        sync.RWMutex
)

// SetGlobalProxy 设置全局HTTP代理
func SetGlobalProxy(proxyURL string, username, password string) error {
	proxyMutex.Lock()
	defer proxyMutex.Unlock()

	// 验证代理URL
	if proxyURL != "" {
		_, err := url.Parse(proxyURL)
		if err != nil {
			return fmt.Errorf("无效的代理URL: %v", err)
		}
	}

	globalProxyConfig = ProxyConfig{
		URL:      proxyURL,
		Username: username,
		Password: password,
		Enabled:  proxyURL != "",
	}

	Logger.Info("已设置全局HTTP代理: %s", proxyURL)
	return nil
}

// DisableGlobalProxy 禁用全局HTTP代理
func DisableGlobalProxy() {
	proxyMutex.Lock()
	defer proxyMutex.Unlock()

	globalProxyConfig.Enabled = false
	Logger.Info("已禁用全局HTTP代理")
}

// GetProxyTransport 获取配置了代理的HTTP Transport
func GetProxyTransport() *http.Transport {
	proxyMutex.RLock()
	defer proxyMutex.RUnlock()

	transport := &http.Transport{}

	if globalProxyConfig.Enabled && globalProxyConfig.URL != "" {
		proxyURL, err := url.Parse(globalProxyConfig.URL)
		if err != nil {
			Logger.Error("解析代理URL失败: %v", err)
			return transport
		}

		// 如果代理URL中没有用户名密码，但在配置中提供了，则添加
		if proxyURL.User == nil && globalProxyConfig.Username != "" {
			proxyURL.User = url.UserPassword(globalProxyConfig.Username, globalProxyConfig.Password)
		}

		transport.Proxy = http.ProxyURL(proxyURL)
		Logger.Debug("使用HTTP代理: %s", proxyURL.Redacted())
	}

	return transport
}

// GetProxyClient 获取配置了代理的HTTP客户端
func GetProxyClient() *http.Client {
	return &http.Client{
		Transport: GetProxyTransport(),
	}
}

// IsProxyEnabled 检查代理是否已启用
func IsProxyEnabled() bool {
	proxyMutex.RLock()
	defer proxyMutex.RUnlock()
	return globalProxyConfig.Enabled
}

// GetProxyConfig 获取当前代理配置
func GetProxyConfig() ProxyConfig {
	proxyMutex.RLock()
	defer proxyMutex.RUnlock()
	return globalProxyConfig
}

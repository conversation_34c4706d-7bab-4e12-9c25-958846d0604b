package utils

import (
	"crypto/sha256"
	"encoding/hex"
	"io/ioutil"
	"net/http"
	"regexp"
	"strings"
	"time"
)

// EnhancedWebFingerprint 增强的网站指纹
type EnhancedWebFingerprint struct {
	WebFingerprint
	FrameworkVersions    map[string]string // 框架版本
	ProgrammingLanguages []string          // 编程语言
	DatabaseTechnologies []string          // 数据库技术
	SecurityFeatures     []string          // 安全特性
	ContentHash          string            // 内容哈希
	ResponseTime         int               // 响应时间(ms)
	ResponseSize         int               // 响应大小(bytes)
	HeaderFingerprints   map[string]string // 头部指纹
	CookieFingerprints   map[string]string // Cookie指纹
}

// EnhancedWebFingerprintDetection 增强的网站指纹检测
func EnhancedWebFingerprintDetection(url string) (*EnhancedWebFingerprint, error) {
	// 获取基本指纹
	basicFingerprint, err := DetectWebFingerprint(url)
	if err != nil {
		return nil, err
	}

	enhancedFingerprint := &EnhancedWebFingerprint{
		WebFingerprint:       *basicFingerprint,
		FrameworkVersions:    make(map[string]string),
		ProgrammingLanguages: []string{},
		DatabaseTechnologies: []string{},
		SecurityFeatures:     []string{},
		HeaderFingerprints:   make(map[string]string),
		CookieFingerprints:   make(map[string]string),
	}

	// 获取完整页面内容
	body, headers, cookies, responseTime, responseSize, err := fetchFullPage(url)
	if err != nil {
		return enhancedFingerprint, nil
	}

	// 计算内容哈希
	enhancedFingerprint.ContentHash = calculateContentHash(body)
	enhancedFingerprint.ResponseTime = responseTime
	enhancedFingerprint.ResponseSize = responseSize

	// 检测框架版本
	enhancedFingerprint.FrameworkVersions = detectFrameworkVersions(body, headers)

	// 检测编程语言
	enhancedFingerprint.ProgrammingLanguages = detectProgrammingLanguages(body, headers, cookies)

	// 检测数据库技术
	enhancedFingerprint.DatabaseTechnologies = detectDatabaseTechnologies(body)

	// 检测安全特性
	enhancedFingerprint.SecurityFeatures = detectSecurityFeatures(headers, body)

	// 分析HTTP头指纹
	enhancedFingerprint.HeaderFingerprints = analyzeHeaderFingerprints(headers)

	// 分析Cookie指纹
	enhancedFingerprint.CookieFingerprints = analyzeCookieFingerprints(cookies)

	return enhancedFingerprint, nil
}

// fetchFullPage 获取完整页面内容
func fetchFullPage(url string) (string, map[string]string, map[string]string, int, int, error) {
	start := time.Now()

	client := &http.Client{
		Timeout: 15 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 10 {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", nil, nil, 0, 0, err
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Connection", "keep-alive")

	resp, err := client.Do(req)
	if err != nil {
		return "", nil, nil, 0, 0, err
	}
	defer resp.Body.Close()

	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", nil, nil, 0, 0, err
	}

	responseTime := int(time.Since(start).Milliseconds())
	responseSize := len(bodyBytes)

	// 提取HTTP头
	headers := make(map[string]string)
	for name, values := range resp.Header {
		headers[strings.ToLower(name)] = strings.Join(values, ", ")
	}

	// 提取Cookies
	cookies := make(map[string]string)
	for _, cookie := range resp.Cookies() {
		cookies[cookie.Name] = cookie.Value
	}

	return string(bodyBytes), headers, cookies, responseTime, responseSize, nil
}

// calculateContentHash 计算内容哈希
func calculateContentHash(content string) string {
	// 移除空白字符和注释，以减少动态内容的影响
	cleanContent := removeWhitespaceAndComments(content)

	hash := sha256.Sum256([]byte(cleanContent))
	return hex.EncodeToString(hash[:])
}

// removeWhitespaceAndComments 移除空白字符和注释
func removeWhitespaceAndComments(content string) string {
	// 移除HTML注释
	commentRegex := regexp.MustCompile(`<!--[\s\S]*?-->`)
	content = commentRegex.ReplaceAllString(content, "")

	// 移除JavaScript注释
	jsCommentRegex := regexp.MustCompile(`//.*?\n|/\*[\s\S]*?\*/`)
	content = jsCommentRegex.ReplaceAllString(content, "")

	// 移除CSS注释
	cssCommentRegex := regexp.MustCompile(`/\*[\s\S]*?\*/`)
	content = cssCommentRegex.ReplaceAllString(content, "")

	// 移除多余空白字符
	whitespaceRegex := regexp.MustCompile(`\s+`)
	content = whitespaceRegex.ReplaceAllString(content, " ")

	return strings.TrimSpace(content)
}

// detectFrameworkVersions 检测框架版本
func detectFrameworkVersions(body string, headers map[string]string) map[string]string {
	versions := make(map[string]string)

	// 检测jQuery版本
	jqueryRegex := regexp.MustCompile(`jquery[.-]([0-9.]+)(?:\.min)?\.js`)
	jqueryMatches := jqueryRegex.FindStringSubmatch(body)
	if len(jqueryMatches) > 1 {
		versions["jQuery"] = jqueryMatches[1]
	}

	// 检测Bootstrap版本
	bootstrapRegex := regexp.MustCompile(`bootstrap[.-]([0-9.]+)(?:\.min)?\.(?:js|css)`)
	bootstrapMatches := bootstrapRegex.FindStringSubmatch(body)
	if len(bootstrapMatches) > 1 {
		versions["Bootstrap"] = bootstrapMatches[1]
	}

	// 检测React版本
	reactRegex := regexp.MustCompile(`react[.-](?:dom[.-])?([0-9.]+)(?:\.min)?\.js`)
	reactMatches := reactRegex.FindStringSubmatch(body)
	if len(reactMatches) > 1 {
		versions["React"] = reactMatches[1]
	}

	// 检测Angular版本
	if strings.Contains(body, "ng-app") || strings.Contains(body, "ng-controller") {
		angularRegex := regexp.MustCompile(`angular[.-]([0-9.]+)(?:\.min)?\.js`)
		angularMatches := angularRegex.FindStringSubmatch(body)
		if len(angularMatches) > 1 {
			versions["Angular"] = angularMatches[1]
		} else if strings.Contains(body, "angular.js") || strings.Contains(body, "angular.min.js") {
			versions["Angular"] = "1.x"
		}
	}

	// 检测Vue.js版本
	vueRegex := regexp.MustCompile(`vue[.-]([0-9.]+)(?:\.min)?\.js`)
	vueMatches := vueRegex.FindStringSubmatch(body)
	if len(vueMatches) > 1 {
		versions["Vue.js"] = vueMatches[1]
	}

	return versions
}

// detectProgrammingLanguages 检测编程语言
func detectProgrammingLanguages(body string, headers map[string]string, cookies map[string]string) []string {
	languages := []string{}

	// 检查HTTP头
	if poweredBy, ok := headers["x-powered-by"]; ok {
		if strings.Contains(poweredBy, "PHP") {
			languages = append(languages, "PHP")
		}
		if strings.Contains(poweredBy, "ASP.NET") {
			languages = append(languages, "C#")
		}
		if strings.Contains(poweredBy, "JSP") || strings.Contains(poweredBy, "Servlet") {
			languages = append(languages, "Java")
		}
		if strings.Contains(poweredBy, "Ruby") {
			languages = append(languages, "Ruby")
		}
	}

	// 检查Cookie
	for name := range cookies {
		if strings.Contains(name, "PHPSESSID") {
			if !contains(languages, "PHP") {
				languages = append(languages, "PHP")
			}
		}
		if strings.Contains(name, "JSESSIONID") {
			if !contains(languages, "Java") {
				languages = append(languages, "Java")
			}
		}
		if strings.Contains(name, "ASP.NET_SessionId") {
			if !contains(languages, "C#") {
				languages = append(languages, "C#")
			}
		}
	}

	// 检查页面内容
	if strings.Contains(body, "wp-content") || strings.Contains(body, "wp-includes") {
		if !contains(languages, "PHP") {
			languages = append(languages, "PHP")
		}
	}

	if strings.Contains(body, "django") {
		languages = append(languages, "Python")
	}

	if strings.Contains(body, "rails") || strings.Contains(body, "ruby on rails") {
		languages = append(languages, "Ruby")
	}

	if strings.Contains(body, "node_modules") || strings.Contains(body, "npm") {
		languages = append(languages, "JavaScript")
	}

	if strings.Contains(body, "react") || strings.Contains(body, "vue") || strings.Contains(body, "angular") {
		if !contains(languages, "JavaScript") {
			languages = append(languages, "JavaScript")
		}
	}

	return languages
}

// detectDatabaseTechnologies 检测数据库技术
func detectDatabaseTechnologies(body string) []string {
	databases := []string{}

	// 检查常见数据库技术的指纹
	dbPatterns := map[string][]string{
		"MySQL": {
			"mysql_error", "MySQL Error", "mysqli", "mysql_connect",
		},
		"PostgreSQL": {
			"pg_connect", "postgresql", "postgres",
		},
		"SQLite": {
			"sqlite_", "SQLite3",
		},
		"MongoDB": {
			"mongodb", "mongoose", "mongo",
		},
		"Oracle": {
			"ORA-", "Oracle Database", "oracle",
		},
		"Microsoft SQL Server": {
			"sqlserver", "mssql", "sql server",
		},
		"Redis": {
			"redis", "Redis",
		},
		"Elasticsearch": {
			"elasticsearch", "elastic",
		},
	}

	for db, patterns := range dbPatterns {
		for _, pattern := range patterns {
			if strings.Contains(strings.ToLower(body), strings.ToLower(pattern)) {
				if !contains(databases, db) {
					databases = append(databases, db)
				}
				break
			}
		}
	}

	return databases
}

// detectSecurityFeatures 检测安全特性
func detectSecurityFeatures(headers map[string]string, body string) []string {
	features := []string{}

	// 检查安全相关的HTTP头
	securityHeaders := map[string]string{
		"content-security-policy":      "Content Security Policy",
		"strict-transport-security":    "HTTP Strict Transport Security",
		"x-content-type-options":       "X-Content-Type-Options",
		"x-frame-options":              "X-Frame-Options",
		"x-xss-protection":             "X-XSS-Protection",
		"referrer-policy":              "Referrer Policy",
		"feature-policy":               "Feature Policy",
		"permissions-policy":           "Permissions Policy",
		"expect-ct":                    "Expect-CT",
		"public-key-pins":              "HTTP Public Key Pinning",
		"cross-origin-embedder-policy": "Cross-Origin Embedder Policy",
		"cross-origin-opener-policy":   "Cross-Origin Opener Policy",
		"cross-origin-resource-policy": "Cross-Origin Resource Policy",
	}

	for header, feature := range securityHeaders {
		if _, ok := headers[header]; ok {
			features = append(features, feature)
		}
	}

	// 检查HTTPS
	if _, ok := headers["strict-transport-security"]; ok {
		features = append(features, "HTTPS")
	}

	// 检查CSRF保护
	csrfPatterns := []string{"csrf", "xsrf", "_token", "authenticity_token"}
	for _, pattern := range csrfPatterns {
		if strings.Contains(strings.ToLower(body), pattern) {
			features = append(features, "CSRF Protection")
			break
		}
	}

	// 检查验证码
	captchaPatterns := []string{"captcha", "recaptcha", "hcaptcha", "turnstile"}
	for _, pattern := range captchaPatterns {
		if strings.Contains(strings.ToLower(body), pattern) {
			features = append(features, "CAPTCHA")
			break
		}
	}

	return features
}

// analyzeHeaderFingerprints 分析HTTP头指纹
func analyzeHeaderFingerprints(headers map[string]string) map[string]string {
	fingerprints := make(map[string]string)

	// 提取服务器指纹
	if server, ok := headers["server"]; ok {
		fingerprints["Server"] = server
	}

	// 提取X-Powered-By指纹
	if poweredBy, ok := headers["x-powered-by"]; ok {
		fingerprints["X-Powered-By"] = poweredBy
	}

	// 提取X-Generator指纹
	if generator, ok := headers["x-generator"]; ok {
		fingerprints["X-Generator"] = generator
	}

	// 提取X-AspNet-Version指纹
	if aspnetVersion, ok := headers["x-aspnet-version"]; ok {
		fingerprints["X-AspNet-Version"] = aspnetVersion
	}

	// 提取X-Drupal-Cache指纹
	if drupalCache, ok := headers["x-drupal-cache"]; ok {
		fingerprints["X-Drupal-Cache"] = drupalCache
	}

	// 提取X-Varnish指纹
	if varnish, ok := headers["x-varnish"]; ok {
		fingerprints["X-Varnish"] = varnish
	}

	// 提取Via指纹
	if via, ok := headers["via"]; ok {
		fingerprints["Via"] = via
	}

	return fingerprints
}

// analyzeCookieFingerprints 分析Cookie指纹
func analyzeCookieFingerprints(cookies map[string]string) map[string]string {
	fingerprints := make(map[string]string)

	// 常见的Cookie指纹
	cookiePatterns := map[string]string{
		"PHPSESSID":         "PHP",
		"JSESSIONID":        "Java",
		"ASP.NET_SessionId": "ASP.NET",
		"CFID":              "ColdFusion",
		"CFTOKEN":           "ColdFusion",
		"laravel_session":   "Laravel",
		"_wordpress_":       "WordPress",
		"wp-":               "WordPress",
		"joomla_":           "Joomla",
		"drupal":            "Drupal",
		"magento":           "Magento",
		"PrestaShop":        "PrestaShop",
		"TYPO3":             "TYPO3",
		"django":            "Django",
		"rails":             "Ruby on Rails",
		"express:sess":      "Express.js",
		"connect.sid":       "Express.js",
	}

	for cookieName := range cookies {
		for pattern, technology := range cookiePatterns {
			if strings.Contains(cookieName, pattern) {
				fingerprints[cookieName] = technology
			}
		}
	}

	return fingerprints
}

// contains 检查字符串是否在切片中
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

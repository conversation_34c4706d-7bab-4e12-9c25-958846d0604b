package utils

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"time"
)

// GeoLocation 地理位置信息
type GeoLocation struct {
	IP           string  // IP地址
	Country      string  // 国家
	CountryCode  string  // 国家代码
	Region       string  // 地区/省份
	City         string  // 城市
	PostalCode   string  // 邮政编码
	Latitude     float64 // 纬度
	Longitude    float64 // 经度
	ISP          string  // 互联网服务提供商
	Organization string  // 组织
	ASN          string  // 自治系统编号
	Timezone     string  // 时区
}

// ipAPIResponse IP-API.com响应结构
type ipAPIResponse struct {
	Status      string  `json:"status"`
	Country     string  `json:"country"`
	CountryCode string  `json:"countryCode"`
	Region      string  `json:"region"`
	RegionName  string  `json:"regionName"`
	City        string  `json:"city"`
	Zip         string  `json:"zip"`
	Lat         float64 `json:"lat"`
	Lon         float64 `json:"lon"`
	Timezone    string  `json:"timezone"`
	ISP         string  `json:"isp"`
	Org         string  `json:"org"`
	AS          string  `json:"as"`
	Query       string  `json:"query"`
	Message     string  `json:"message"`
}

// GetIPGeolocation 获取IP地址的地理位置信息
func GetIPGeolocation(ipStr string) (*GeoLocation, error) {
	// 解析IP地址
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return nil, fmt.Errorf("无效的IP地址: %s", ipStr)
	}

	// 检查是否为私有IP
	if isPrivateIP(ip) {
		return &GeoLocation{
			IP:          ipStr,
			Country:     "Reserved",
			CountryCode: "ZZ",
			City:        "Private Network",
		}, nil
	}

	// 使用IP-API.com查询地理位置信息
	apiURL := fmt.Sprintf("http://ip-api.com/json/%s?fields=status,message,country,countryCode,region,regionName,city,zip,lat,lon,timezone,isp,org,as,query", ipStr)
	
	client := &http.Client{
		Timeout: 5 * time.Second,
	}
	
	resp, err := client.Get(apiURL)
	if err != nil {
		return nil, fmt.Errorf("IP地理位置API请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取API响应失败: %v", err)
	}
	
	var apiResp ipAPIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析API响应失败: %v", err)
	}
	
	// 检查API响应状态
	if apiResp.Status != "success" {
		return nil, fmt.Errorf("IP地理位置API返回错误: %s", apiResp.Message)
	}
	
	// 提取ASN编号
	asn := ""
	if apiResp.AS != "" {
		// 通常AS格式为"AS12345 Some ISP"，我们只需要数字部分
		for i := 0; i < len(apiResp.AS); i++ {
			if apiResp.AS[i] >= '0' && apiResp.AS[i] <= '9' {
				asn += string(apiResp.AS[i])
			} else if apiResp.AS[i] != 'A' && apiResp.AS[i] != 'S' && apiResp.AS[i] != ' ' {
				break
			}
		}
	}
	
	// 创建地理位置信息
	geoLocation := &GeoLocation{
		IP:           ipStr,
		Country:      apiResp.Country,
		CountryCode:  apiResp.CountryCode,
		Region:       apiResp.RegionName,
		City:         apiResp.City,
		PostalCode:   apiResp.Zip,
		Latitude:     apiResp.Lat,
		Longitude:    apiResp.Lon,
		ISP:          apiResp.ISP,
		Organization: apiResp.Org,
		ASN:          asn,
		Timezone:     apiResp.Timezone,
	}
	
	return geoLocation, nil
}

// isPrivateIP 检查IP是否为私有IP
func isPrivateIP(ip net.IP) bool {
	// 检查是否为IPv4私有地址
	if ip4 := ip.To4(); ip4 != nil {
		// 10.0.0.0/8
		if ip4[0] == 10 {
			return true
		}
		// **********/12
		if ip4[0] == 172 && ip4[1] >= 16 && ip4[1] <= 31 {
			return true
		}
		// ***********/16
		if ip4[0] == 192 && ip4[1] == 168 {
			return true
		}
		// *********/8
		if ip4[0] == 127 {
			return true
		}
	}
	
	// 检查是否为IPv6本地地址
	if ip.IsLoopback() || ip.IsLinkLocalUnicast() || ip.IsLinkLocalMulticast() {
		return true
	}
	
	return false
}

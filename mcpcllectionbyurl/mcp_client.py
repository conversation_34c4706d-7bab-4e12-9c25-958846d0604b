#!/usr/bin/env python3
import argparse
import json
import requests
import sys

def analyze_domain(url, api_url="http://localhost:8080", tls_check=False, fingerprint=False, verbose=False):
    """
    使用MCP API分析域名或URL
    
    参数:
        url (str): 要分析的URL或域名
        api_url (str): MCP API的基础URL
        tls_check (bool): 是否执行TLS安全评估
        fingerprint (bool): 是否执行网站指纹识别
        verbose (bool): 是否输出详细信息
    
    返回:
        dict: 分析结果
    """
    # 构建请求参数
    params = {
        "url": url,
    }
    
    if tls_check:
        params["tls_check"] = "true"
    
    if fingerprint:
        params["fingerprint"] = "true"
    
    if verbose:
        params["verbose"] = "true"
    
    # 发送请求
    try:
        response = requests.get(f"{api_url}/api/analyze", params=params)
        response.raise_for_status()  # 如果响应状态码不是200，抛出异常
        
        result = response.json()
        if not result.get("success"):
            print(f"API错误: {result.get('error')}", file=sys.stderr)
            return None
        
        return result.get("data")
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}", file=sys.stderr)
        return None
    except json.JSONDecodeError as e:
        print(f"解析JSON失败: {e}", file=sys.stderr)
        return None

def print_summary(result):
    """打印分析结果摘要"""
    if not result:
        return
    
    print("\n=== MCP分析结果摘要 ===")
    print(f"域名: {result.get('domain')}")
    print(f"IP地址: {result.get('ip')}")
    
    # 打印ASN信息
    if "ip_to_asn" in result and result["ip_to_asn"]:
        asn_info = result["ip_to_asn"]
        print(f"ASN: {asn_info.get('asn')}")
        print(f"ISP: {asn_info.get('isp')}")
    
    # 打印CDN信息
    if "cdn_info" in result and result["cdn_info"]:
        cdn_info = result["cdn_info"]
        print(f"CDN提供商: {cdn_info.get('provider')}")
        print(f"CDN置信度: {cdn_info.get('confidence')}%")
    
    # 打印地理位置信息
    if "advanced_analysis" in result and "geo_location" in result["advanced_analysis"]:
        geo = result["advanced_analysis"]["geo_location"]
        print(f"地理位置: {geo.get('city')}, {geo.get('country')} ({geo.get('country_code')})")
    
    # 打印TLS安全信息
    if "advanced_analysis" in result and "tls_security" in result["advanced_analysis"]:
        tls = result["advanced_analysis"]["tls_security"]
        print(f"TLS安全评级: {tls.get('security_rating')}")
        print(f"支持的TLS版本: {', '.join(tls.get('supported_versions', []))}")
    
    # 打印网站指纹信息
    if "advanced_analysis" in result and "web_fingerprint" in result["advanced_analysis"]:
        fp = result["advanced_analysis"]["web_fingerprint"]
        print(f"服务器类型: {fp.get('server_type')} {fp.get('server_version')}")
        if fp.get("cms"):
            print(f"CMS: {fp.get('cms')} {fp.get('cms_version')}")
        if fp.get("programming_languages"):
            print(f"编程语言: {', '.join(fp.get('programming_languages', []))}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="MCP API客户端")
    parser.add_argument("url", help="要分析的URL或域名")
    parser.add_argument("--api", default="http://localhost:8080", help="MCP API的基础URL")
    parser.add_argument("--tls", action="store_true", help="执行TLS安全评估")
    parser.add_argument("--fingerprint", action="store_true", help="执行网站指纹识别")
    parser.add_argument("--verbose", action="store_true", help="输出详细信息")
    parser.add_argument("--output", help="输出文件路径")
    parser.add_argument("--summary", action="store_true", help="打印结果摘要")
    
    args = parser.parse_args()
    
    result = analyze_domain(
        args.url, 
        api_url=args.api, 
        tls_check=args.tls, 
        fingerprint=args.fingerprint, 
        verbose=args.verbose
    )
    
    if result:
        # 打印摘要
        if args.summary:
            print_summary(result)
        
        # 输出到文件或标准输出
        if args.output:
            with open(args.output, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到 {args.output}")
        else:
            print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        sys.exit(1)

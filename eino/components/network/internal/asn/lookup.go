/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package asn

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/cloudwego/eino/components/network/schema"
)

// asnAPIResponse represents the response from IP-API.com
type asnAPIResponse struct {
	Status       string `json:"status"`
	ASN          string `json:"as"`
	ISP          string `json:"isp"`
	Organization string `json:"org"`
	Message      string `json:"message"`
}

// Lookup provides ASN lookup capabilities
type Lookup struct {
	client  *http.Client
	timeout time.Duration
	apiURL  string
}

// NewLookup creates a new ASN lookup service
func NewLookup(timeout time.Duration) *Lookup {
	if timeout == 0 {
		timeout = 5 * time.Second
	}
	
	return &Lookup{
		client: &http.Client{
			Timeout: timeout,
		},
		timeout: timeout,
		apiURL:  "http://ip-api.com/json/%s?fields=status,message,as,isp,org",
	}
}

// LookupASN queries ASN information for an IP address
func (l *Lookup) LookupASN(ctx context.Context, ipStr string) (*schema.IPASNInfo, error) {
	if ipStr == "" {
		return &schema.IPASNInfo{
			ASN:     "NA",
			ISP:     "No IP Address",
			RawData: "",
		}, nil
	}

	// Parse IP address
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return &schema.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "Invalid IP Address",
			RawData: "",
		}, fmt.Errorf("invalid IP address: %s", ipStr)
	}

	// Check if it's a private IP
	if isPrivateIP(ip) {
		return &schema.IPASNInfo{
			ASN:     "PRIVATE",
			ISP:     "Private Network",
			RawData: "Private IP Address Range",
		}, nil
	}

	// Query ASN information from API
	return l.queryASNAPI(ctx, ipStr)
}

// queryASNAPI queries ASN information from IP-API.com
func (l *Lookup) queryASNAPI(ctx context.Context, ipStr string) (*schema.IPASNInfo, error) {
	apiURL := fmt.Sprintf(l.apiURL, ipStr)
	
	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return &schema.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("Failed to create request: %v", err),
		}, err
	}

	resp, err := l.client.Do(req)
	if err != nil {
		return &schema.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("API request failed: %v", err),
		}, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return &schema.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("Failed to read API response: %v", err),
		}, err
	}

	var apiResp asnAPIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return &schema.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("Failed to parse API response: %v", err),
		}, err
	}

	// Check API response status
	if apiResp.Status != "success" {
		return &schema.IPASNInfo{
			ASN:     "ERROR",
			ISP:     "",
			RawData: fmt.Sprintf("API returned error: %s", apiResp.Message),
		}, fmt.Errorf("API error: %s", apiResp.Message)
	}

	// Extract ASN number
	asn := "AS-UNKNOWN"
	if apiResp.ASN != "" {
		// Usually AS format is "AS12345 Some ISP", we only need the AS part
		parts := strings.Split(apiResp.ASN, " ")
		if len(parts) > 0 {
			asn = parts[0]
		}
	}

	return &schema.IPASNInfo{
		ASN:     asn,
		ISP:     apiResp.ISP,
		RawData: string(body),
	}, nil
}

// isPrivateIP checks if an IP address is in a private range
func isPrivateIP(ip net.IP) bool {
	// IPv4 private ranges
	if ip.To4() != nil {
		// 10.0.0.0/8
		if ip[0] == 10 {
			return true
		}
		// **********/12
		if ip[0] == 172 && ip[1] >= 16 && ip[1] <= 31 {
			return true
		}
		// ***********/16
		if ip[0] == 192 && ip[1] == 168 {
			return true
		}
		// *********/8 (loopback)
		if ip[0] == 127 {
			return true
		}
		// ***********/16 (link-local)
		if ip[0] == 169 && ip[1] == 254 {
			return true
		}
	} else {
		// IPv6 private ranges
		// ::1/128 (loopback)
		if ip.IsLoopback() {
			return true
		}
		// fe80::/10 (link-local)
		if ip[0] == 0xfe && (ip[1]&0xc0) == 0x80 {
			return true
		}
		// fc00::/7 (unique local)
		if (ip[0] & 0xfe) == 0xfc {
			return true
		}
	}
	
	return false
}

// BatchLookup performs ASN lookup for multiple IP addresses
func (l *Lookup) BatchLookup(ctx context.Context, ips []string) ([]*schema.IPASNInfo, error) {
	results := make([]*schema.IPASNInfo, len(ips))
	
	for i, ip := range ips {
		result, err := l.LookupASN(ctx, ip)
		if err != nil {
			// Continue with error result
			results[i] = &schema.IPASNInfo{
				ASN:     "ERROR",
				ISP:     "",
				RawData: fmt.Sprintf("Lookup failed: %v", err),
			}
		} else {
			results[i] = result
		}
	}
	
	return results, nil
}

package utils

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"mcpcllectionbyurl/models"
	"os"
	"strings"
)

// ExportFormat 导出格式类型
type ExportFormat string

const (
	// FormatJSON JSON格式
	FormatJSON ExportFormat = "json"
	// FormatCSV CSV格式
	FormatCSV ExportFormat = "csv"
)

// ExportOptions 导出选项
type ExportOptions struct {
	Format     ExportFormat // 导出格式
	OutputFile string       // 输出文件路径
	Pretty     bool         // 是否美化输出（仅适用于JSON）
}

// ExportResults 导出结果到文件
func ExportResults(results []*models.MCPResponse, options ExportOptions) error {
	switch options.Format {
	case FormatJSON:
		return exportToJSON(results, options)
	case FormatCSV:
		return exportToCSV(results, options)
	default:
		return fmt.Errorf("不支持的导出格式: %s", options.Format)
	}
}

// exportToJSON 导出结果为JSON格式
func exportToJSON(results []*models.MCPResponse, options ExportOptions) error {
	var jsonData []byte
	var err error

	if options.Pretty {
		jsonData, err = json.MarshalIndent(results, "", "  ")
	} else {
		jsonData, err = json.Marshal(results)
	}

	if err != nil {
		return fmt.Errorf("JSON编码失败: %v", err)
	}

	err = os.WriteFile(options.OutputFile, jsonData, 0644)
	if err != nil {
		return fmt.Errorf("写入JSON文件失败: %v", err)
	}

	Logger.Info("已将结果导出为JSON格式: %s", options.OutputFile)
	return nil
}

// exportToCSV 导出结果为CSV格式
func exportToCSV(results []*models.MCPResponse, options ExportOptions) error {
	file, err := os.Create(options.OutputFile)
	if err != nil {
		return fmt.Errorf("创建CSV文件失败: %v", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入CSV头部
	headers := []string{
		"Domain",
		"IP",
		"IsIPAccess",
		"TLS_Available",
		"WHOIS_Success",
		"Company_Name",
		"ASN",
		"ISP",
		"CDN_Provider",
		"CDN_Confidence",
		"DNS_Server",
		"A_Records",
	}
	if err := writer.Write(headers); err != nil {
		return fmt.Errorf("写入CSV头部失败: %v", err)
	}

	// 写入每个结果的行
	for _, result := range results {
		// 准备CSV行数据
		row := []string{
			result.Domain,
			result.IP,
			fmt.Sprintf("%t", result.IsIPAccess),
			fmt.Sprintf("%t", result.TLSPem != ""),
			fmt.Sprintf("%t", result.Success),
			getCompanyName(result),
			result.IPToASN.ASN,
			result.IPToASN.ISP,
			getCDNProvider(result),
			getCDNConfidence(result),
			result.DNSServerUsed,
			strings.Join(result.ARecords, "; "),
		}

		if err := writer.Write(row); err != nil {
			return fmt.Errorf("写入CSV行失败: %v", err)
		}
	}

	Logger.Info("已将结果导出为CSV格式: %s", options.OutputFile)
	return nil
}

// getCompanyName 获取公司名称
func getCompanyName(result *models.MCPResponse) string {
	if result.Company != nil {
		return result.Company.CompanyName
	}
	return ""
}

// getCDNProvider 获取CDN提供商
func getCDNProvider(result *models.MCPResponse) string {
	if provider, ok := result.CDNHeaders["provider"]; ok {
		return provider
	}
	return ""
}

// getCDNConfidence 获取CDN置信度
func getCDNConfidence(result *models.MCPResponse) string {
	if confidence, ok := result.CDNHeaders["confidence"]; ok {
		return confidence
	}
	return ""
}

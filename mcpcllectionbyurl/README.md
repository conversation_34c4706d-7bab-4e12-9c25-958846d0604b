# MCP Collection By URL

MCP (Multi-Channel Profiling) Collection By URL 是一个高级网络情报收集工具，使用Go语言开发，用于全面收集和分析域名或URL的各种信息。该工具专为网络安全分析、资产管理和仿冒检测设计，提供丰富的数据收集功能：

- DNS信息（A记录、CNAME记录等）
- TLS/SSL证书详情和安全评估
- WHOIS信息和注册商数据
- ASN（自治系统编号）数据和网络归属
- CDN头部检测和提供商识别（支持15+主流CDN）
- 公司信息提取和组织结构分析
- 全球地理位置信息（国家、城市、经纬度）
- 增强的TLS安全评估（支持多种检测方法）
- 网站技术栈识别（框架、编程语言、数据库等）
- 安全特性检测（HTTPS、CSP、HSTS等）
- 网站指纹识别和内容哈希计算

## 功能特性

### 基础功能

- URL解析和主机名提取（支持各种格式的URL）
- 支持域名和IP地址作为输入
- 批量处理多个URL（高效并发处理）
- 可自定义日志级别（INFO、DEBUG、WARN、ERROR）
- 多种输出格式（JSON和CSV）
- HTTP代理支持（带认证）
- 超时控制和错误处理机制

### 网络信息收集

- DNS解析（A记录、CNAME记录）
- ASN查询（支持IPv4和IPv6）
- WHOIS数据收集与智能解析
- 地理位置信息查询（国家、城市、经纬度、时区）
- 网络运营商识别

### 安全评估

- TLS证书获取和PEM编码
- 增强的TLS安全评估（多种检测方法）
- 支持TLS 1.0/1.1/1.2/1.3版本检测
- 密码套件强度分析
- TLS漏洞检测（BEAST、POODLE、Heartbleed等）
- 证书链验证和信任状态评估
- 安全评级和改进建议

### CDN和网站技术分析

- 智能CDN检测和提供商识别（15+主流CDN）
- 多层CDN架构检测
- 网站技术栈识别（服务器、CMS、框架等）
- 编程语言检测（PHP、Python、Java等）
- 数据库技术识别
- 安全特性检测（CSP、HSTS、XSS保护等）
- 网站指纹识别和内容哈希计算

## 安装

### 前提条件

- Go 1.16或更高版本
- OpenSSL（可选，用于增强TLS检测）
- GnuTLS（可选，用于增强TLS检测）
- WHOIS命令行工具

### 从源代码构建

1. 克隆仓库：

   ```bash
   git clone https://github.com/yourusername/mcpcllectionbyurl.git
   cd mcpcllectionbyurl
   ```

2. 安装依赖：

   ```bash
   go mod download
   ```

3. 构建应用：

   ```bash
   go build
   ```

### 使用Docker（可选）

1. 构建Docker镜像：

   ```bash
   docker build -t mcpcllectionbyurl .
   ```

2. 运行Docker容器：

   ```bash
   docker run --rm mcpcllectionbyurl -url=https://example.com
   ```

## 使用方法

### 基本用法

使用URL作为参数运行应用：

```bash
./mcpcllectionbyurl -url=https://example.com
```

或者简单地：

```bash
./mcpcllectionbyurl https://example.com
```

### 命令行选项

应用支持以下命令行选项：

- `-url=<url>`: 要分析的URL
- `-output=<file>`: 结果输出文件路径（默认：标准输出）
- `-format=<format>`: 输出格式，支持json和csv（默认：json）
- `-verbose`: 启用详细输出
- `-debug`: 启用调试日志
- `-batch=<file>`: 从文件批量处理多个URL（每行一个URL）
- `-concurrent=<num>`: 批处理时的最大并发数（默认：5）
- `-proxy=<url>`: HTTP代理URL（格式：`http://host:port`）
- `-proxy-user=<user>`: HTTP代理用户名
- `-proxy-pass=<pass>`: HTTP代理密码
- `-timeout=<seconds>`: 请求超时时间（默认：30秒）
- `-retry=<num>`: 失败请求的重试次数（默认：3）
- `-tls-check=<bool>`: 启用TLS安全评估（默认：false）
- `-fingerprint=<bool>`: 启用网站指纹识别（默认：false）
- `-geo=<bool>`: 启用地理位置信息查询（默认：true）
- `-whois=<bool>`: 启用WHOIS信息查询（默认：true）
- `-cdn=<bool>`: 启用CDN检测（默认：true）
- `-version`: 显示版本信息

### 批量处理

要一次处理多个URL，创建一个文本文件，每行一个URL：

```text
https://example.com
https://google.com
# 这是一个注释
https://github.com
```

然后使用`-batch`选项运行应用：

```bash
./mcpcllectionbyurl -batch=urls.txt -output=results.json -concurrent=10
```

### 使用HTTP代理

如果需要通过代理访问网站，可以使用以下选项：

```bash
./mcpcllectionbyurl -url=https://example.com -proxy=http://proxy.example.com:8080 -proxy-user=username -proxy-pass=password
```

### 导出为CSV格式

要将结果导出为CSV格式：

```bash
./mcpcllectionbyurl -url=https://example.com -output=result.csv -format=csv
```

## 外部API和依赖

应用使用以下外部API和工具获取数据：

- **IP-API.com**: 用于获取IP地址的地理位置和ASN信息
- **WHOIS命令**: 用于获取域名的WHOIS信息
- **OpenSSL**: 用于获取TLS证书信息和安全评估
- **GnuTLS**: 作为TLS检测的备选方法
- **Go标准库**: 用于DNS解析、HTTP请求和TLS连接

## 项目结构

- `main.go`: 应用程序入口点
- `handler/`: 包含主要请求处理逻辑
  - `handler/handle.go`: 核心处理函数
  - `handler/options.go`: 命令行选项处理
- `models/`: 数据模型和响应结构
  - `models/response.go`: 响应数据结构
  - `models/options.go`: 选项数据结构
- `utils/`: 各种操作的实用函数
  - `utils/asn.go`: ASN查询功能
  - `utils/cdn.go`: CDN头部检测和提供商识别
  - `utils/cdn_providers.go`: CDN提供商规则库
  - `utils/dns.go`: DNS解析
  - `utils/logger.go`: 日志工具
  - `utils/tls.go`: TLS证书获取
  - `utils/enhanced_ssl_check.go`: 增强的TLS检测
  - `utils/url.go`: URL解析和验证
  - `utils/whois.go`: WHOIS数据获取和缓存
  - `utils/proxy.go`: HTTP代理支持
  - `utils/export.go`: 结果导出功能（JSON和CSV）
  - `utils/geo.go`: 地理位置信息查询
  - `utils/fingerprint.go`: 网站指纹识别
  - `utils/enhanced_fingerprint.go`: 增强的网站指纹识别
- `data/`: 应用程序使用的数据文件
  - `data/cdn_rules.json`: CDN识别规则
  - `data/fingerprints.json`: 网站指纹规则

## 性能优化

- WHOIS数据缓存，减少外部API调用
- 高效的ASN查询（使用实时API查询）
- 外部命令和HTTP请求的超时处理
- 批处理操作的并发处理
- 可配置的并发级别
- HTTP连接池优化

## CDN检测

应用可以检测以下CDN提供商：

- Cloudflare
- Akamai
- Fastly
- Amazon CloudFront
- Varnish
- Google Cloud CDN
- Azure CDN
- Alibaba Cloud CDN
- Tencent Cloud CDN
- Baidu Cloud CDN
- Imperva Incapsula
- Sucuri
- Limelight
- StackPath
- KeyCDN
- Nginx（作为可能的CDN）

## 增强的TLS检测

MCP工具使用多种方法进行TLS安全评估，提高检测成功率：

- 原生Go TLS库检测
- OpenSSL命令行检测
- GnuTLS命令行检测
- 自动重试和回退机制
- 支持多种TLS版本和密码套件
- 证书链验证和信任状态评估
- 漏洞检测和安全评级

## 增强的网站指纹识别

MCP工具提供全面的网站指纹识别功能：

- 框架版本检测（jQuery、Bootstrap、React等）
- 编程语言检测（PHP、JavaScript、Python等）
- 数据库技术检测（MySQL、PostgreSQL等）
- 安全特性检测（HTTPS、CSP、HSTS等）
- 内容哈希计算（用于网站内容变化检测）
- HTTP头和Cookie指纹分析
- 响应时间和响应大小统计

## 应用场景

MCP工具适用于以下场景：

- **网络安全分析**：评估域名和IP的安全状态，检测潜在威胁
- **资产管理**：识别和分类组织的网络资产
- **仿冒检测**：收集特征数据，用于检测仿冒网站
- **合规性检查**：评估TLS配置是否符合安全标准
- **技术栈分析**：识别网站使用的技术和框架
- **CDN配置验证**：检查CDN配置和性能
- **网络情报收集**：获取域名和IP的综合信息

### 仿冒检测应用

MCP工具特别适合用于仿冒网站检测，通过收集以下特征数据：

1. **内容哈希**：计算网页内容的哈希值，用于比较相似度
2. **技术栈指纹**：识别网站使用的框架、库和版本
3. **证书信息**：分析TLS证书的颁发者、有效期和主题
4. **HTTP头特征**：收集和分析HTTP响应头
5. **CDN配置**：检测CDN提供商和配置
6. **域名注册信息**：比较WHOIS数据中的注册商和注册日期
7. **服务器特征**：识别服务器类型、版本和配置

这些数据可以用于构建仿冒检测系统，通过比较可疑网站与合法网站的特征，识别潜在的仿冒行为。

## 许可证

本项目采用MIT许可证 - 详情请参阅LICENSE文件。

## 贡献

欢迎提交问题报告、功能请求和代码贡献。请遵循以下步骤：

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 更新日志

### v1.2.0 (2025-05-23)

- 增强了TLS检测功能，支持多种检测方法
- 添加了网站指纹识别增强功能
- 改进了CDN检测，支持15+主流CDN提供商
- 添加了内容哈希计算功能
- 优化了错误处理和重试机制
- 改进了日志输出

### v1.1.0 (2025-04-15)

- 添加了地理位置信息查询
- 添加了TLS安全评估功能
- 添加了网站技术栈识别
- 改进了WHOIS数据解析
- 添加了HTTP代理支持

### v1.0.0 (2025-03-01)

- 首次发布
- 基本的URL分析功能
- DNS解析和ASN查询
- WHOIS数据收集
- CDN头部检测

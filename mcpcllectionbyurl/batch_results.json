[{"domain": "https://github.com", "ip": "**************", "is_ip_access": false, "tls_pem": "-----B<PERSON><PERSON> CERTIFICATE-----\nMIIEoTCCBEigAwIBAgIRAKtmhrVie+gFloITMBKGSfUwCgYIKoZIzj0EAwIwgY8x\nCzAJBgNVBAYTAkdCMRswGQYDVQQIExJHcmVhdGVyIE1hbmNoZXN0ZXIxEDAOBgNV\nBAcTB1NhbGZvcmQxGDAWBgNVBAoTD1NlY3RpZ28gTGltaXRlZDE3MDUGA1UEAxMu\nU2VjdGlnbyBFQ0MgRG9tYWluIFZhbGlkYXRpb24gU2VjdXJlIFNlcnZlciBDQTAe\nFw0yNTAyMDUwMDAwMDBaFw0yNjAyMDUyMzU5NTlaMBUxEzARBgNVBAMTCmdpdGh1\nYi5jb20wWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAAQgNFxG/yzL+CSarvC7L3ep\nH5chNnG6wiYYxR5D/Z1J4MxGnIX8KbT5fCgLoyzHXL9v50bdBIq6y4AtN4gN7gbW\no4IC/DCCAvgwHwYDVR0jBBgwFoAU9oUKOxGG4QR9DqoLLNLuzGR7e64wHQYDVR0O\nBBYEFFPIf96emE7HTda83quVPjA9PdHIMA4GA1UdDwEB/wQEAwIHgDAMBgNVHRMB\nAf8EAjAAMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjBJBgNVHSAEQjBA\nMDQGCysGAQQBsjEBAgIHMCUwIwYIKwYBBQUHAgEWF2h0dHBzOi8vc2VjdGlnby5j\nb20vQ1BTMAgGBmeBDAECATCBhAYIKwYBBQUHAQEEeDB2ME8GCCsGAQUFBzAChkNo\ndHRwOi8vY3J0LnNlY3RpZ28uY29tL1NlY3RpZ29FQ0NEb21haW5WYWxpZGF0aW9u\nU2VjdXJlU2VydmVyQ0EuY3J0MCMGCCsGAQUFBzABhhdodHRwOi8vb2NzcC5zZWN0\naWdvLmNvbTCCAX4GCisGAQQB1nkCBAIEggFuBIIBagFoAHUAlpdkv1VYl633Q4do\nNwhCd+nwOtX2pPM2bkakPw/KqcYAAAGU02uUSwAABAMARjBEAiA7i6o+LpQjt6Ae\nEjltHhs/TiECnHd0xTeer/3vD1xgsAIgYlGwRot+SqEBCs//frx/YHTPwox9QLdy\n7GjTLWHfcMAAdwAZhtTHKKpv/roDb3gqTQGRqs4tcjEPrs5dcEEtJUzH1AAAAZTT\na5PtAAAEAwBIMEYCIQDlrInx7J+3MfqgxB2+Fvq3dMlk1qj4chOw/+HkYVfG0AIh\nAMT+JKAQfUuIdBGxfryrGrwsOD3pRs1tyAyykdPGRgsTAHYAyzj3FYl8hKFEX1vB\n3fvJbvKaWc1HCmkFhbDLFMMUWOcAAAGU02uUJQAABAMARzBFAiEA1GKW92agDFNJ\nIYrMH3gaJdXsdIVpUcZOfxH1FksbuLECIFJCfslINhc53Q0TIMJHdcFOW2tgG4tB\nA1dL881tXbMnMCUGA1UdEQQeMByCCmdpdGh1Yi5jb22CDnd3dy5naXRodWIuY29t\nMAoGCCqGSM49BAMCA0cAMEQCIHGMp27BBBJ1356lCe2WYyzYIp/fAONQM3AkeE/f\nym0sAiBtVfN3YgIZ+neHEfwcRhhz4uDpc8F+tKmtceWJSicMkA==\n-----END CERTIFICATE-----\n", "openssl_raw": "Connecting to **************\nCONNECTION ESTABLISHED\nProtocol version: TLSv1.3\nCiphersuite: TLS_AES_128_GCM_SHA256\nPeer certificate: CN=github.com\nHash used: SHA256\nSignature type: ECDSA\nVerification: OK\nServer Temp Key: X25519, 253 bits\nDONE\n", "raw_whois": "% IANA WHOIS server\n% for more information on IANA, visit http://www.iana.org\n% This query returned 1 object\n\nrefer:        whois.verisign-grs.com\n\ndomain:       COM\n\norganisation: VeriSign Global Registry Services\naddress:      12061 Bluemont Way\naddress:      Reston VA 20190\naddress:      United States of America (the)\n\ncontact:      administrative\nname:         Registry Customer Service\norganisation: VeriSign Global Registry Services\naddress:      12061 Bluemont Way\naddress:      Reston VA 20190\naddress:      United States of America (the)\nphone:        ****** 925-6999\nfax-no:       ****** 948 3978\ne-mail:       <EMAIL>\n\ncontact:      technical\nname:         Registry Customer Service\norganisation: VeriSign Global Registry Services\naddress:      12061 Bluemont Way\naddress:      Reston VA 20190\naddress:      United States of America (the)\nphone:        ****** 925-6999\nfax-no:       ****** 948 3978\ne-mail:       <EMAIL>\n\nnserver:      A.GTLD-SERVERS.NET ********** 2001:503:a83e:0:0:0:2:30\nnserver:      B.GTLD-SERVERS.NET ************ 2001:503:231d:0:0:0:2:30\nnserver:      C.GTLD-SERVERS.NET ************ 2001:503:83eb:0:0:0:0:30\nnserver:      D.GTLD-SERVERS.NET ************ 2001:500:856e:0:0:0:0:30\nnserver:      E.GTLD-SERVERS.NET ************ 2001:502:1ca1:0:0:0:0:30\nnserver:      F.GTLD-SERVERS.NET ************ 2001:503:d414:0:0:0:0:30\nnserver:      G.GTLD-SERVERS.NET ************ 2001:503:eea3:0:0:0:0:30\nnserver:      H.GTLD-SERVERS.NET ************* 2001:502:8cc:0:0:0:0:30\nnserver:      I.GTLD-SERVERS.NET ************* 2001:503:39c1:0:0:0:0:30\nnserver:      J.GTLD-SERVERS.NET ************ 2001:502:7094:0:0:0:0:30\nnserver:      K.GTLD-SERVERS.NET ************* 2001:503:d2d:0:0:0:0:30\nnserver:      L.GTLD-SERVERS.NET ************* 2001:500:d937:0:0:0:0:30\nnserver:      M.GTLD-SERVERS.NET 192.55.83.30 2001:501:b1f9:0:0:0:0:30\nds-rdata:     19718 13 2 8acbb0cd28f41250a80a491389424d341522d946b0da0c0291f2d3d771d7805a\n\nwhois:        whois.verisign-grs.com\n\nstatus:       ACTIVE\nremarks:      Registration information: http://www.verisigninc.com\n\ncreated:      1985-01-01\nchanged:      2023-12-07\nsource:       IANA\n\n# whois.verisign-grs.com\n\n   Domain Name: GITHUB.COM\r\n   Registry Domain ID: 1264983250_DOMAIN_COM-VRSN\r\n   Registrar WHOIS Server: whois.markmonitor.com\r\n   Registrar URL: http://www.markmonitor.com\r\n   Updated Date: 2024-09-07T09:16:32Z\r\n   Creation Date: 2007-10-09T18:20:50Z\r\n   Registry Expiry Date: 2026-10-09T18:20:50Z\r\n   Registrar: MarkMonitor Inc.\r\n   Registrar IANA ID: 292\r\n   Registrar Abuse Contact Email: <EMAIL>\r\n   Registrar Abuse Contact Phone: *************\r\n   Domain Status: clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited\r\n   Domain Status: clientTransferProhibited https://icann.org/epp#clientTransferProhibited\r\n   Domain Status: clientUpdateProhibited https://icann.org/epp#clientUpdateProhibited\r\n   Name Server: DNS1.P08.NSONE.NET\r\n   Name Server: DNS2.P08.NSONE.NET\r\n   Name Server: DNS3.P08.NSONE.NET\r\n   Name Server: DNS4.P08.NSONE.NET\r\n   Name Server: NS-1283.AWSDNS-32.ORG\r\n   Name Server: NS-1707.AWSDNS-21.CO.UK\r\n   Name Server: NS-421.AWSDNS-52.COM\r\n   Name Server: NS-520.AWSDNS-01.NET\r\n   DNSSEC: unsigned\r\n   URL of the ICANN Whois Inaccuracy Complaint Form: https://www.icann.org/wicf/\r\n>>> Last update of whois database: 2025-05-23T01:52:29Z <<<\r\n\n# whois.markmonitor.com\n\nDomain Name: github.com\nRegistry Domain ID: 1264983250_DOMAIN_COM-VRSN\nRegistrar WHOIS Server: whois.markmonitor.com\nRegistrar URL: http://www.markmonitor.com\nUpdated Date: 2024-09-07T09:16:33+0000\nCreation Date: 2007-10-09T18:20:50+0000\nRegistrar Registration Expiration Date: 2026-10-09T00:00:00+0000\nRegistrar: MarkMonitor, Inc.\nRegistrar IANA ID: 292\nRegistrar Abuse Contact Email: <EMAIL>\nRegistrar Abuse Contact Phone: *************\nDomain Status: clientUpdateProhibited (https://www.icann.org/epp#clientUpdateProhibited)\nDomain Status: clientTransferProhibited (https://www.icann.org/epp#clientTransferProhibited)\nDomain Status: clientDeleteProhibited (https://www.icann.org/epp#clientDeleteProhibited)\nRegistrant Organization: GitHub, Inc.\nRegistrant State/Province: CA\nRegistrant Country: US\nRegistrant Email: Select Request Email Form at https://domains.markmonitor.com/whois/github.com\nAdmin Organization: GitHub, Inc.\nAdmin State/Province: CA\nAdmin Country: US\nAdmin Email: Select Request Email Form at https://domains.markmonitor.com/whois/github.com\nTech Organization: GitHub, Inc.\nTech State/Province: CA\nTech Country: US\nTech Email: Select Request Email Form at https://domains.markmonitor.com/whois/github.com\nName Server: dns2.p08.nsone.net\nName Server: dns3.p08.nsone.net\nName Server: ns-1283.awsdns-32.org\nName Server: ns-520.awsdns-01.net\nName Server: dns1.p08.nsone.net\nName Server: ns-421.awsdns-52.com\nName Server: ns-1707.awsdns-21.co.uk\nName Server: dns4.p08.nsone.net\nDNSSEC: unsigned\nURL of the ICANN WHOIS Data Problem Reporting System: http://wdprs.internic.net/\n>>> Last update of WHOIS database: 2025-05-23T01:51:16+0000 <<<\n\n", "whois_runtime_ms": 1313, "success": true, "company": {"company_name": "GitHub, Inc.", "legal_person": "", "status": "clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited", "registration_number": "1264983250_DOMAIN_COM-VRSN"}, "a_records": ["**************"], "dns_server_used": "system", "ip_to_asn": {"asn": "AS8075", "isp": "Microsoft Corporation", "raw_data": "{\"status\":\"success\",\"isp\":\"Microsoft Corporation\",\"org\":\"Microsoft Azure Cloud (southeastasia)\",\"as\":\"AS8075 Microsoft Corporation\"}"}, "cdn_headers": {"server": "github.com"}, "raw_headers": {"accept-ranges": "bytes", "cache-control": "max-age=0, private, must-revalidate", "content-language": "en-US", "content-security-policy": "default-src 'none'; base-uri 'self'; child-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/; connect-src 'self' uploads.github.com www.githubstatus.com collector.github.com raw.githubusercontent.com api.github.com github-cloud.s3.amazonaws.com github-production-repository-file-5c1aeb.s3.amazonaws.com github-production-upload-manifest-file-7fdce7.s3.amazonaws.com github-production-user-asset-6210df.s3.amazonaws.com *.rel.tunnels.api.visualstudio.com wss://*.rel.tunnels.api.visualstudio.com objects-origin.githubusercontent.com copilot-proxy.githubusercontent.com proxy.individual.githubcopilot.com proxy.business.githubcopilot.com proxy.enterprise.githubcopilot.com *.actions.githubusercontent.com wss://*.actions.githubusercontent.com productionresultssa0.blob.core.windows.net/ productionresultssa1.blob.core.windows.net/ productionresultssa2.blob.core.windows.net/ productionresultssa3.blob.core.windows.net/ productionresultssa4.blob.core.windows.net/ productionresultssa5.blob.core.windows.net/ productionresultssa6.blob.core.windows.net/ productionresultssa7.blob.core.windows.net/ productionresultssa8.blob.core.windows.net/ productionresultssa9.blob.core.windows.net/ productionresultssa10.blob.core.windows.net/ productionresultssa11.blob.core.windows.net/ productionresultssa12.blob.core.windows.net/ productionresultssa13.blob.core.windows.net/ productionresultssa14.blob.core.windows.net/ productionresultssa15.blob.core.windows.net/ productionresultssa16.blob.core.windows.net/ productionresultssa17.blob.core.windows.net/ productionresultssa18.blob.core.windows.net/ productionresultssa19.blob.core.windows.net/ github-production-repository-image-32fea6.s3.amazonaws.com github-production-release-asset-2e65be.s3.amazonaws.com insights.github.com wss://alive.github.com api.githubcopilot.com api.individual.githubcopilot.com api.business.githubcopilot.com api.enterprise.githubcopilot.com github.githubassets.com; font-src github.githubassets.com; form-action 'self' github.com gist.github.com copilot-workspace.githubnext.com objects-origin.githubusercontent.com; frame-ancestors 'none'; frame-src viewscreen.githubusercontent.com notebooks.githubusercontent.com www.youtube-nocookie.com; img-src 'self' data: blob: github.githubassets.com media.githubusercontent.com camo.githubusercontent.com identicons.github.com avatars.githubusercontent.com private-avatars.githubusercontent.com github-cloud.s3.amazonaws.com objects.githubusercontent.com release-assets.githubusercontent.com secured-user-images.githubusercontent.com/ user-images.githubusercontent.com/ private-user-images.githubusercontent.com opengraph.githubassets.com copilotprodattachments.blob.core.windows.net/github-production-copilot-attachments/ github-production-user-asset-6210df.s3.amazonaws.com customer-stories-feed.github.com spotlights-feed.github.com objects-origin.githubusercontent.com *.githubusercontent.com; manifest-src 'self'; media-src github.com user-images.githubusercontent.com/ secured-user-images.githubusercontent.com/ private-user-images.githubusercontent.com github-production-user-asset-6210df.s3.amazonaws.com gist.github.com github.githubassets.com; script-src github.githubassets.com; style-src 'unsafe-inline' github.githubassets.com; upgrade-insecure-requests; worker-src github.githubassets.com github.com/assets-cdn/worker/ github.com/assets/ gist.github.com/assets-cdn/worker/", "content-type": "text/html; charset=utf-8", "date": "Fri, 23 May 2025 01:52:44 GMT", "etag": "W/\"90a1697df434d06caf3a44d99c35e986\"", "referrer-policy": "origin-when-cross-origin, strict-origin-when-cross-origin", "server": "github.com", "set-cookie": "_gh_sess=r7v921RTI%2FUroIPbE3POzXlkqE9QxOA%2FzBn6rAZWviUkbonrce7wY7gRhLSCA%2FGDVBMHRZ6rARsmnijKf%2F6yHtuhlKX0mVILuIj78ipTHOpemY44QHdRgxFLqlwlVUMzp3mvg2Ya2gTjoQykR%2Bu%2BBAOrvUGisPkbVj2zN09Y7COzJ4wmK2FqyjBO%2BEg1JNmlR08RSMQ9ZzKU3JaAlwGJ8TgOH%2BhkEEJHecGN4bUfgAv5kSu5ssfmXwwObsNtYZeEqgxMNb7hYHCxpF5GvyvHtw%3D%3D--s02j9TboTLKAI4vy--FGd%2Bler%2BDY5Ew5TYw%2BxbOA%3D%3D; Path=/; HttpOnly; Secure; SameSite=Lax; _octo=GH1.1.1277933935.1747965168; Path=/; Domain=github.com; Expires=Sat, 23 May 2026 01:52:48 GMT; Secure; SameSite=Lax; logged_in=no; Path=/; Domain=github.com; Expires=Sat, 23 May 2026 01:52:48 GMT; HttpOnly; Secure; SameSite=Lax", "strict-transport-security": "max-age=31536000; includeSubdomains; preload", "vary": "X-<PERSON><PERSON><PERSON><PERSON>, X-P<PERSON><PERSON><PERSON>-Container, Turbo-Visit, Turbo-Frame, Accept-Language,Accept-Encoding, Accept, X-Requested-With", "x-content-type-options": "nosniff", "x-frame-options": "deny", "x-github-request-id": "0A4A:1A2F74:44440B:546A69:682FD4F0", "x-xss-protection": "0"}, "advanced_analysis": {"geo_location": {"country": "Singapore", "country_code": "SG", "region": "Central Singapore", "city": "Singapore", "postal_code": "168812", "latitude": 1.283, "longitude": 103.833, "timezone": "Asia/Singapore"}, "tls_security": {"supported_versions": ["TLS 1.2", "TLS 1.3"], "certificate_info": {"Issuer": "CN=Sectigo ECC Domain Validation Secure Server CA,O=Sectigo Limited,L=Salford,ST=Greater Manchester,C=GB", "NotAfter": "2026-02-05 23:59:59", "NotBefore": "2025-02-05 00:00:00", "SerialNumber": "227830333772454795620750445496253172213", "Status": "有效", "Subject": "CN=github.com", "SubjectAltName": "github.com, www.github.com", "Version": "3"}, "cipher_suites": null, "vulnerabilities": {"BEAST": false, "Heartbleed": false, "POODLE": false}, "security_rating": "A", "recommendations": ["当前TLS配置良好，继续保持"]}, "web_fingerprint": {"server_type": "github.com", "server_version": "", "cms": "", "cms_version": "", "frameworks": null, "javascript": null, "analytics": null, "technologies": null, "confidence": 20}}}, {"domain": "https://example.com", "ip": "************", "is_ip_access": false, "tls_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIFmzCCBSGgAwIBAgIQCtiTuvposLf7ekBPBuyvmjAKBggqhkjOPQQDAzBZMQsw\nCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMTMwMQYDVQQDEypEaWdp\nQ2VydCBHbG9iYWwgRzMgVExTIEVDQyBTSEEzODQgMjAyMCBDQTEwHhcNMjUwMTE1\nMDAwMDAwWhcNMjYwMTE1MjM1OTU5WjCBjjELMAkGA1UEBhMCVVMxEzARBgNVBAgT\nCkNhbGlmb3JuaWExFDASBgNVBAcTC0xvcyBBbmdlbGVzMTwwOgYDVQQKEzNJbnRl\ncm5ldCBDb3Jwb3JhdGlvbiBmb3IgQXNzaWduZWQgTmFtZXMgYW5kIE51bWJlcnMx\nFjAUBgNVBAMMDSouZXhhbXBsZS5jb20wWTATBgcqhkjOPQIBBggqhkjOPQMBBwNC\nAASaSJeELWFsCMlqFKDIOIDmAMCH+plXDhsA4tiHklfnCPs8XrDThCg3wSQRjtMg\ncXS9k49OCQPOAjuw5GZzz6/uo4IDkzCCA48wHwYDVR0jBBgwFoAUiiPrnmvX+Tdd\n+W0hOXaaoWfeEKgwHQYDVR0OBBYEFPDBajIN7NrH6o/NDW0ZElnRvnLtMCUGA1Ud\nEQQeMByCDSouZXhhbXBsZS5jb22CC2V4YW1wbGUuY29tMD4GA1UdIAQ3MDUwMwYG\nZ4EMAQICMCkwJwYIKwYBBQUHAgEWG2h0dHA6Ly93d3cuZGlnaWNlcnQuY29tL0NQ\nUzAOBgNVHQ8BAf8EBAMCA4gwHQYDVR0lBBYwFAYIKwYBBQUHAwEGCCsGAQUFBwMC\nMIGfBgNVHR8EgZcwgZQwSKBGoESGQmh0dHA6Ly9jcmwzLmRpZ2ljZXJ0LmNvbS9E\naWdpQ2VydEdsb2JhbEczVExTRUNDU0hBMzg0MjAyMENBMS0yLmNybDBIoEagRIZC\naHR0cDovL2NybDQuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsRzNUTFNFQ0NT\nSEEzODQyMDIwQ0ExLTIuY3JsMIGHBggrBgEFBQcBAQR7MHkwJAYIKwYBBQUHMAGG\nGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBRBggrBgEFBQcwAoZFaHR0cDovL2Nh\nY2VydHMuZGlnaWNlcnQuY29tL0RpZ2lDZXJ0R2xvYmFsRzNUTFNFQ0NTSEEzODQy\nMDIwQ0ExLTIuY3J0MAwGA1UdEwEB/wQCMAAwggF7BgorBgEEAdZ5AgQCBIIBawSC\nAWcBZQB0AA5XlLzzrqk+MxssmQez95Dfm8I9cTIl3SGpJaxhxU4hAAABlGd6v8cA\nAAQDAEUwQwIfJBcPWkx80ik7uLYW6OGvNYvJ4NmOR2RXc9uviFPH6QIgUtuuUenH\nIT5UNWJffBBRq31tUGi7ZDTSrrM0f4z1Va4AdQBkEcRspBLsp4kcogIuALyrTygH\n1B41J6vq/tUDyX3N8AAAAZRnesAFAAAEAwBGMEQCIHCu6NgHhV1Qvif/G7BHq7ci\nMGH8jdch/xy4LzrYlesXAiByMFMvDhGg4sYm1MsrDGVedcwpE4eN0RuZcFGmWxwJ\ncgB2AEmcm2neHXzs/DbezYdkprhbrwqHgBnRVVL76esp3fjDAAABlGd6wBkAAAQD\nAEcwRQIgaFh67yEQ2lwgm3X16n2iWjEQFII2b2fpONtBVibZVWwCIQD5psqjXDYs\nIEb1hyh0S8bBN3O4u2sA9zisKIlYjZg8wjAKBggqhkjOPQQDAwNoADBlAjEA+aaC\nRlPbb+VY+u4avPyaG7fvUDJqN8KwlrXD4XptT7QL+D03+BA/FUEo3dD1iz37AjBk\nY3jhsuLAW7pWsDbtX/Qwxp6kNsK4jh1/RjvV/260sxQwM/GM7t0+T0uP2L+Y12U=\n-----END CERTIFICATE-----\n", "openssl_raw": "Connecting to ************\nCONNECTION ESTABLISHED\nProtocol version: TLSv1.3\nCiphersuite: TLS_AES_256_GCM_SHA384\nPeer certificate: C=US, ST=California, L=Los Angeles, O=Internet Corporation for Assigned Names and Numbers, CN=*.example.com\nHash used: SHA256\nSignature type: ECDSA\nVerification: OK\nServer Temp Key: X25519, 253 bits\nDONE\n", "raw_whois": "% IANA WHOIS server\n% for more information on IANA, visit http://www.iana.org\n% This query returned 1 object\n\ndomain:       EXAMPLE.COM\n\norganisation: Internet Assigned Numbers Authority\n\ncreated:      1992-01-01\nsource:       IANA\n\n", "whois_runtime_ms": 517, "success": true, "a_records": ["************", "************", "*************", "*************", "************", "************"], "dns_server_used": "system", "ip_to_asn": {"asn": "AS20940", "isp": "Akamai International B.V.", "raw_data": "{\"status\":\"success\",\"isp\":\"Akamai International B.V.\",\"org\":\"Akamai Technologies, Inc.\",\"as\":\"AS20940 Akamai International B.V.\"}"}, "cdn_headers": {}, "raw_headers": {"alt-svc": "h3=\":443\"; ma=93600,h3-29=\":443\"; ma=93600,quic=\":443\"; ma=93600; v=\"43\"", "cache-control": "max-age=1334", "content-type": "text/html", "date": "Fri, 23 May 2025 01:52:49 GMT", "etag": "\"84238dfc8092e5d9c0dac8ef93371a07:1736799080.121134\"", "last-modified": "Mon, 13 Jan 2025 20:11:20 GMT"}, "advanced_analysis": {"geo_location": {"country": "United States", "country_code": "US", "region": "California", "city": "El Segundo", "postal_code": "90245", "latitude": 33.9214, "longitude": -118.413, "timezone": "America/Los_Angeles"}, "tls_security": {"supported_versions": ["TLS 1.2", "TLS 1.3"], "certificate_info": {"Issuer": "CN=DigiCert Global G3 TLS ECC SHA384 2020 CA1,O=DigiCert Inc,C=US", "NotAfter": "2026-01-15 23:59:59", "NotBefore": "2025-01-15 00:00:00", "SerialNumber": "14416812407440461216471976375640436634", "Status": "有效", "Subject": "CN=*.example.com,O=Internet Corporation for Assigned Names and Numbers,L=Los Angeles,ST=California,C=US", "SubjectAltName": "*.example.com, example.com", "Version": "3"}, "cipher_suites": null, "vulnerabilities": {"BEAST": false, "Heartbleed": false, "POODLE": false}, "security_rating": "A", "recommendations": ["当前TLS配置良好，继续保持"]}, "web_fingerprint": {"server_type": "", "server_version": "", "cms": "", "cms_version": "", "frameworks": null, "javascript": null, "analytics": null, "technologies": null, "confidence": 0}}}, {"domain": "https://microsoft.com", "ip": "*************", "is_ip_access": false, "tls_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----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\nyOI=\n-----END CERTIFICATE-----\n", "openssl_raw": "Connecting to *************\nCONNECTION ESTABLISHED\nProtocol version: TLSv1.3\nCiphersuite: TLS_AES_256_GCM_SHA384\nPeer certificate: C=US, ST=WA, L=Redmond, O=Microsoft Corporation, CN=microsoft.com\nHash used: SHA256\nSignature type: RSA-PSS\nVerification: OK\nServer Temp Key: ECDH, prime256v1, 256 bits\nDONE\n", "raw_whois": "% IANA WHOIS server\n% for more information on IANA, visit http://www.iana.org\n% This query returned 1 object\n\nrefer:        whois.verisign-grs.com\n\ndomain:       COM\n\norganisation: VeriSign Global Registry Services\naddress:      12061 Bluemont Way\naddress:      Reston VA 20190\naddress:      United States of America (the)\n\ncontact:      administrative\nname:         Registry Customer Service\norganisation: VeriSign Global Registry Services\naddress:      12061 Bluemont Way\naddress:      Reston VA 20190\naddress:      United States of America (the)\nphone:        ****** 925-6999\nfax-no:       ****** 948 3978\ne-mail:       <EMAIL>\n\ncontact:      technical\nname:         Registry Customer Service\norganisation: VeriSign Global Registry Services\naddress:      12061 Bluemont Way\naddress:      Reston VA 20190\naddress:      United States of America (the)\nphone:        ****** 925-6999\nfax-no:       ****** 948 3978\ne-mail:       <EMAIL>\n\nnserver:      A.GTLD-SERVERS.NET ********** 2001:503:a83e:0:0:0:2:30\nnserver:      B.GTLD-SERVERS.NET ************ 2001:503:231d:0:0:0:2:30\nnserver:      C.GTLD-SERVERS.NET ************ 2001:503:83eb:0:0:0:0:30\nnserver:      D.GTLD-SERVERS.NET ************ 2001:500:856e:0:0:0:0:30\nnserver:      E.GTLD-SERVERS.NET ************ 2001:502:1ca1:0:0:0:0:30\nnserver:      F.GTLD-SERVERS.NET ************ 2001:503:d414:0:0:0:0:30\nnserver:      G.GTLD-SERVERS.NET ************ 2001:503:eea3:0:0:0:0:30\nnserver:      H.GTLD-SERVERS.NET ************* 2001:502:8cc:0:0:0:0:30\nnserver:      I.GTLD-SERVERS.NET ************* 2001:503:39c1:0:0:0:0:30\nnserver:      J.GTLD-SERVERS.NET ************ 2001:502:7094:0:0:0:0:30\nnserver:      K.GTLD-SERVERS.NET ************* 2001:503:d2d:0:0:0:0:30\nnserver:      L.GTLD-SERVERS.NET ************* 2001:500:d937:0:0:0:0:30\nnserver:      M.GTLD-SERVERS.NET 192.55.83.30 2001:501:b1f9:0:0:0:0:30\nds-rdata:     19718 13 2 8acbb0cd28f41250a80a491389424d341522d946b0da0c0291f2d3d771d7805a\n\nwhois:        whois.verisign-grs.com\n\nstatus:       ACTIVE\nremarks:      Registration information: http://www.verisigninc.com\n\ncreated:      1985-01-01\nchanged:      2023-12-07\nsource:       IANA\n\n# whois.verisign-grs.com\n\n   Domain Name: MICROSOFT.COM\r\n   Registry Domain ID: 2724960_DOMAIN_COM-VRSN\r\n   Registrar WHOIS Server: whois.markmonitor.com\r\n   Registrar URL: http://www.markmonitor.com\r\n   Updated Date: 2025-04-01T12:38:29Z\r\n   Creation Date: 1991-05-02T04:00:00Z\r\n   Registry Expiry Date: 2026-05-03T04:00:00Z\r\n   Registrar: MarkMonitor Inc.\r\n   Registrar IANA ID: 292\r\n   Registrar Abuse Contact Email: <EMAIL>\r\n   Registrar Abuse Contact Phone: *************\r\n   Domain Status: clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited\r\n   Domain Status: clientTransferProhibited https://icann.org/epp#clientTransferProhibited\r\n   Domain Status: clientUpdateProhibited https://icann.org/epp#clientUpdateProhibited\r\n   Domain Status: serverDeleteProhibited https://icann.org/epp#serverDeleteProhibited\r\n   Domain Status: serverTransferProhibited https://icann.org/epp#serverTransferProhibited\r\n   Domain Status: serverUpdateProhibited https://icann.org/epp#serverUpdateProhibited\r\n   Name Server: NS1-39.AZURE-DNS.COM\r\n   Name Server: NS2-39.AZURE-DNS.NET\r\n   Name Server: NS3-39.AZURE-DNS.ORG\r\n   Name Server: NS4-39.AZURE-DNS.INFO\r\n   DNSSEC: unsigned\r\n   URL of the ICANN Whois Inaccuracy Complaint Form: https://www.icann.org/wicf/\r\n>>> Last update of whois database: 2025-05-23T01:52:29Z <<<\r\n\n# whois.markmonitor.com\n\n", "whois_runtime_ms": 1412, "success": true, "company": {"company_name": "", "legal_person": "", "status": "clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited", "registration_number": "2724960_DOMAIN_COM-VRSN"}, "a_records": ["*************"], "dns_server_used": "system", "ip_to_asn": {"asn": "AS8075", "isp": "Microsoft Corporation", "raw_data": "{\"status\":\"success\",\"isp\":\"Microsoft Corporation\",\"org\":\"Microsoft Corporation\",\"as\":\"AS8075 Microsoft Corporation\"}"}, "cdn_headers": {"server": "AkamaiNetStorage"}, "raw_headers": {"content-type": "text/html", "date": "Fri, 23 May 2025 01:52:49 GMT", "etag": "\"85de642e1467807f64f7e10807df3869:1711562737.176211\"", "last-modified": "<PERSON><PERSON>, 26 Mar 2024 18:16:43 GMT", "server": "AkamaiNetStorage"}, "advanced_analysis": {"geo_location": {"country": "Canada", "country_code": "CA", "region": "Ontario", "city": "Toronto", "postal_code": "M5A", "latitude": 43.6532, "longitude": -79.3832, "timezone": "America/Toronto"}, "tls_security": {"supported_versions": ["TLS 1.2", "TLS 1.3"], "certificate_info": {"Issuer": "CN=Microsoft Azure RSA TLS Issuing CA 07,O=Microsoft Corporation,C=US", "NotAfter": "2025-11-08 05:02:25", "NotBefore": "2025-05-12 05:02:25", "SerialNumber": "1137500030088086204880307764586391724558657032", "Status": "有效", "Subject": "CN=microsoft.com,O=Microsoft Corporation,L=Redmond,ST=WA,C=US", "SubjectAltName": "microsoft.com, s.microsoft.com, ga.microsoft.com, aep.microsoft.com, aer.microsoft.com, grv.microsoft.com, hup.microsoft.com, mac.microsoft.com, mkb.microsoft.com, pme.microsoft.com, pmi.microsoft.com, rss.microsoft.com, sar.microsoft.com, tco.microsoft.com, fuse.microsoft.com, ieak.microsoft.com, mac2.microsoft.com, mcsp.microsoft.com, open.microsoft.com, shop.microsoft.com, spur.microsoft.com, itpro.microsoft.com, mango.microsoft.com, music.microsoft.com, pymes.microsoft.com, store.microsoft.com, aether.microsoft.com, alerts.microsoft.com, design.microsoft.com, garage.microsoft.com, gigjam.microsoft.com, msctec.microsoft.com, online.microsoft.com, stream.microsoft.com, afflink.microsoft.com, connect.microsoft.com, develop.microsoft.com, domains.microsoft.com, example.microsoft.com, madeira.microsoft.com, msdnisv.microsoft.com, mspress.microsoft.com, www.aep.microsoft.com, www.aer.microsoft.com, wwwbeta.microsoft.com, business.microsoft.com, empresas.microsoft.com, learning.microsoft.com, msdnwiki.microsoft.com, openness.microsoft.com, pinpoint.microsoft.com, snackbox.microsoft.com, sponsors.microsoft.com, stationq.microsoft.com, aistories.microsoft.com, community.microsoft.com, crawlmsdn.microsoft.com, iotschool.microsoft.com, messenger.microsoft.com, minecraft.microsoft.com, backoffice.microsoft.com, enterprise.microsoft.com, iotcentral.microsoft.com, pinunblock.microsoft.com, reroute443.microsoft.com, communities.microsoft.com, explore-smb.microsoft.com, expressions.microsoft.com, ondernemers.microsoft.com, techacademy.microsoft.com, terraserver.microsoft.com, communities2.microsoft.com, connectevent.microsoft.com, dataplatform.microsoft.com, entrepreneur.microsoft.com, hxd.research.microsoft.com, mspartnerira.microsoft.com, mydatahealth.microsoft.com, oemcommunity.microsoft.com, real-stories.microsoft.com, www.formspro.microsoft.com, futuredecoded.microsoft.com, upgradecenter.microsoft.com, learnanalytics.microsoft.com, onlinelearning.microsoft.com, businesscentral.microsoft.com, cloud-immersion.microsoft.com, studentpartners.microsoft.com, analyticspartner.microsoft.com, businessplatform.microsoft.com, explore-security.microsoft.com, kleinunternehmen.microsoft.com, partnercommunity.microsoft.com, explore-marketing.microsoft.com, innovationcontest.microsoft.com, partnerincentives.microsoft.com, phoenixcataloguat.microsoft.com, szkolyprzyszlosci.microsoft.com, www.powerautomate.microsoft.com, successionplanning.microsoft.com, lumiaconversationsuk.microsoft.com, successionplanninguat.microsoft.com, businessmobilitycenter.microsoft.com, skypeandteams.fasttrack.microsoft.com, www.microsoftdlapartnerow.microsoft.com, commercialappcertification.microsoft.com, www.skypeandteams.fasttrack.microsoft.com, ceoconnections.event.microsoft.com, biz4afrika.microsoft.com, cashback.microsoft.com, www.cashback.microsoft.com, visio.microsoft.com, insidemsr.microsoft.com, developervelocityassessment.com, www.developervelocityassessment.com, gears5.com, www.gears5.com, www.gearstactics.com, gearstactics.com, m12.microsoft.com, seeingai.com, yourchoice.microsoft.com, mvtd.events.microsoft.com, imagine.microsoft.com, microsoft.com.au, www.microsoft.com.au, dynamics.microsoft.com, powerplatform.microsoft.com, powerapps.microsoft.com, powerautomate.microsoft.com, powervirtualagents.microsoft.com, powerpages.microsoft.com, test.ideas.fabric.microsoft.com, sds.microsoft.com, ppe.sds.microsoft.com, www.microsoft365copilot.com, www.jclarity.com, techinnovatorsspotlight.com, www.techinnovatorsspotlight.com, copilot.ai, getlicensingready.com, www.getlicensingready.com, jpn.delve.office.com, aus.delve.office.com, ind.delve.office.com, kor.delve.office.com, cobra.me.microsoft.com, www.businesscentral.com, businesscentral.com, msaidatastudio.officeppe.net, ideas.fabric.microsoft.com, www.cpt.link, cpt.link, yarp.dot.net, microsoftstream.com, www.microsoftstream.com, web.microsoftstream.com, discover.copilot.ai, copilot.com, www.copilot.com, discover.copilot.com", "Version": "3"}, "cipher_suites": null, "vulnerabilities": {"BEAST": false, "Heartbleed": false, "POODLE": false}, "security_rating": "A", "recommendations": ["当前TLS配置良好，继续保持"]}}}, {"domain": "https://google.com", "ip": "**********", "is_ip_access": false, "tls_pem": "", "openssl_raw": "tls dial error: dial tcp **********:443: i/o timeout", "raw_whois": "% IANA WHOIS server\n% for more information on IANA, visit http://www.iana.org\n% This query returned 1 object\n\nrefer:        whois.verisign-grs.com\n\ndomain:       COM\n\norganisation: VeriSign Global Registry Services\naddress:      12061 Bluemont Way\naddress:      Reston VA 20190\naddress:      United States of America (the)\n\ncontact:      administrative\nname:         Registry Customer Service\norganisation: VeriSign Global Registry Services\naddress:      12061 Bluemont Way\naddress:      Reston VA 20190\naddress:      United States of America (the)\nphone:        ****** 925-6999\nfax-no:       ****** 948 3978\ne-mail:       <EMAIL>\n\ncontact:      technical\nname:         Registry Customer Service\norganisation: VeriSign Global Registry Services\naddress:      12061 Bluemont Way\naddress:      Reston VA 20190\naddress:      United States of America (the)\nphone:        ****** 925-6999\nfax-no:       ****** 948 3978\ne-mail:       <EMAIL>\n\nnserver:      A.GTLD-SERVERS.NET ********** 2001:503:a83e:0:0:0:2:30\nnserver:      B.GTLD-SERVERS.NET ************ 2001:503:231d:0:0:0:2:30\nnserver:      C.GTLD-SERVERS.NET ************ 2001:503:83eb:0:0:0:0:30\nnserver:      D.GTLD-SERVERS.NET ************ 2001:500:856e:0:0:0:0:30\nnserver:      E.GTLD-SERVERS.NET ************ 2001:502:1ca1:0:0:0:0:30\nnserver:      F.GTLD-SERVERS.NET ************ 2001:503:d414:0:0:0:0:30\nnserver:      G.GTLD-SERVERS.NET ************ 2001:503:eea3:0:0:0:0:30\nnserver:      H.GTLD-SERVERS.NET ************* 2001:502:8cc:0:0:0:0:30\nnserver:      I.GTLD-SERVERS.NET ************* 2001:503:39c1:0:0:0:0:30\nnserver:      J.GTLD-SERVERS.NET ************ 2001:502:7094:0:0:0:0:30\nnserver:      K.GTLD-SERVERS.NET ************* 2001:503:d2d:0:0:0:0:30\nnserver:      L.GTLD-SERVERS.NET ************* 2001:500:d937:0:0:0:0:30\nnserver:      M.GTLD-SERVERS.NET 192.55.83.30 2001:501:b1f9:0:0:0:0:30\nds-rdata:     19718 13 2 8acbb0cd28f41250a80a491389424d341522d946b0da0c0291f2d3d771d7805a\n\nwhois:        whois.verisign-grs.com\n\nstatus:       ACTIVE\nremarks:      Registration information: http://www.verisigninc.com\n\ncreated:      1985-01-01\nchanged:      2023-12-07\nsource:       IANA\n\n# whois.verisign-grs.com\n\n   Domain Name: GOOGLE.COM\r\n   Registry Domain ID: 2138514_DOMAIN_COM-VRSN\r\n   Registrar WHOIS Server: whois.markmonitor.com\r\n   Registrar URL: http://www.markmonitor.com\r\n   Updated Date: 2019-09-09T15:39:04Z\r\n   Creation Date: 1997-09-15T04:00:00Z\r\n   Registry Expiry Date: 2028-09-14T04:00:00Z\r\n   Registrar: MarkMonitor Inc.\r\n   Registrar IANA ID: 292\r\n   Registrar Abuse Contact Email: <EMAIL>\r\n   Registrar Abuse Contact Phone: *************\r\n   Domain Status: clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited\r\n   Domain Status: clientTransferProhibited https://icann.org/epp#clientTransferProhibited\r\n   Domain Status: clientUpdateProhibited https://icann.org/epp#clientUpdateProhibited\r\n   Domain Status: serverDeleteProhibited https://icann.org/epp#serverDeleteProhibited\r\n   Domain Status: serverTransferProhibited https://icann.org/epp#serverTransferProhibited\r\n   Domain Status: serverUpdateProhibited https://icann.org/epp#serverUpdateProhibited\r\n   Name Server: NS1.GOOGLE.COM\r\n   Name Server: NS2.GOOGLE.COM\r\n   Name Server: NS3.GOOGLE.COM\r\n   Name Server: NS4.GOOGLE.COM\r\n   DNSSEC: unsigned\r\n   URL of the ICANN Whois Inaccuracy Complaint Form: https://www.icann.org/wicf/\r\n>>> Last update of whois database: 2025-05-23T01:52:44Z <<<\r\n\n# whois.markmonitor.com\n\nDomain Name: google.com\nRegistry Domain ID: 2138514_DOMAIN_COM-VRSN\nRegistrar WHOIS Server: whois.markmonitor.com\nRegistrar URL: http://www.markmonitor.com\nUpdated Date: 2024-08-02T02:17:33+0000\nCreation Date: 1997-09-15T07:00:00+0000\nRegistrar Registration Expiration Date: 2028-09-13T07:00:00+0000\nRegistrar: MarkMonitor, Inc.\nRegistrar IANA ID: 292\nRegistrar Abuse Contact Email: <EMAIL>\nRegistrar Abuse Contact Phone: *************\nDomain Status: clientUpdateProhibited (https://www.icann.org/epp#clientUpdateProhibited)\nDomain Status: clientTransferProhibited (https://www.icann.org/epp#clientTransferProhibited)\nDomain Status: clientDeleteProhibited (https://www.icann.org/epp#clientDeleteProhibited)\nDomain Status: serverUpdateProhibited (https://www.icann.org/epp#serverUpdateProhibited)\nDomain Status: serverTransferProhibited (https://www.icann.org/epp#serverTransferProhibited)\nDomain Status: serverDeleteProhibited (https://www.icann.org/epp#serverDeleteProhibited)\nRegistrant Organization: Google LLC\nRegistrant State/Province: CA\nRegistrant Country: US\nRegistrant Email: Select Request Email Form at https://domains.markmonitor.com/whois/google.com\nAdmin Organization: Google LLC\nAdmin State/Province: CA\nAdmin Country: US\nAdmin Email: Select Request Email Form at https://domains.markmonitor.com/whois/google.com\nTech Organization: Google LLC\nTech State/Province: CA\nTech Country: US\nTech Email: Select Request Email Form at https://domains.markmonitor.com/whois/google.com\nName Server: ns4.google.com\nName Server: ns2.google.com\nName Server: ns3.google.com\nName Server: ns1.google.com\nDNSSEC: unsigned\nURL of the ICANN WHOIS Data Problem Reporting System: http://wdprs.internic.net/\n>>> Last update of WHOIS database: 2025-05-23T01:51:39+0000 <<<\n\n", "whois_runtime_ms": 3022, "success": true, "company": {"company_name": "Google LLC", "legal_person": "", "status": "clientDeleteProhibited https://icann.org/epp#clientDeleteProhibited", "registration_number": "2138514_DOMAIN_COM-VRSN"}, "a_records": ["**********"], "dns_server_used": "system", "ip_to_asn": {"asn": "AS3356", "isp": "Level 3 Communications, Inc.", "raw_data": "{\"status\":\"success\",\"isp\":\"Level 3 Communications, Inc.\",\"org\":\"Level 3, LLC\",\"as\":\"AS3356 Level 3 Parent, LLC\"}"}, "cdn_headers": {"error": "Head \"https://google.com\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}, "raw_headers": {}, "advanced_analysis": {"geo_location": {"country": "United States", "country_code": "US", "region": "Louisiana", "city": "<PERSON>", "postal_code": "71203", "latitude": 32.5891, "longitude": -92.0664, "timezone": "America/Chicago"}}}]
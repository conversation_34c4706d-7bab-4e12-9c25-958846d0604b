package models

// IPASNInfo ASN信息
type IPASNInfo struct {
	ASN     string `json:"asn"`
	ISP     string `json:"isp"`
	RawData string `json:"raw_data"`
}

// GeoLocationInfo 地理位置信息
type GeoLocationInfo struct {
	Country     string  `json:"country"`
	CountryCode string  `json:"country_code"`
	Region      string  `json:"region"`
	City        string  `json:"city"`
	PostalCode  string  `json:"postal_code"`
	Latitude    float64 `json:"latitude"`
	Longitude   float64 `json:"longitude"`
	Timezone    string  `json:"timezone"`
}

// TLSSecurityInfo TLS安全信息
type TLSSecurityInfo struct {
	SupportedVersions    []string          `json:"supported_versions"`
	CertificateInfo      map[string]string `json:"certificate_info"`
	CipherSuites         []CipherSuiteInfo `json:"cipher_suites"`
	Vulnerabilities      map[string]bool   `json:"vulnerabilities"`
	SecurityRating       string            `json:"security_rating"`
	Recommendations      []string          `json:"recommendations"`
	ConnectionSuccessful bool              `json:"connection_successful,omitempty"`
	ErrorMessage         string            `json:"error_message,omitempty"`
	UsedMethod           string            `json:"used_method,omitempty"`
	TLSExtensions        []string          `json:"tls_extensions,omitempty"`
	ALPN                 []string          `json:"alpn,omitempty"`
	OCSP                 bool              `json:"ocsp,omitempty"`
	CT                   bool              `json:"ct,omitempty"`
	SessionTickets       bool              `json:"session_tickets,omitempty"`
	SNI                  bool              `json:"sni,omitempty"`
	RetryCount           int               `json:"retry_count,omitempty"`
	TotalTime            int               `json:"total_time,omitempty"`
}

// CipherSuiteInfo 密码套件信息
type CipherSuiteInfo struct {
	Name     string `json:"name"`
	Strength string `json:"strength"`
}

// WebFingerprintInfo 网站指纹信息
type WebFingerprintInfo struct {
	ServerType           string            `json:"server_type"`
	ServerVersion        string            `json:"server_version"`
	CMS                  string            `json:"cms"`
	CMSVersion           string            `json:"cms_version"`
	Frameworks           []string          `json:"frameworks"`
	JavaScript           []string          `json:"javascript"`
	Analytics            []string          `json:"analytics"`
	Technologies         []string          `json:"technologies"`
	Confidence           int               `json:"confidence"`
	FrameworkVersions    map[string]string `json:"framework_versions,omitempty"`
	ProgrammingLanguages []string          `json:"programming_languages,omitempty"`
	DatabaseTechnologies []string          `json:"database_technologies,omitempty"`
	SecurityFeatures     []string          `json:"security_features,omitempty"`
	ContentHash          string            `json:"content_hash,omitempty"`
	ResponseTime         int               `json:"response_time,omitempty"`
	ResponseSize         int               `json:"response_size,omitempty"`
	HeaderFingerprints   map[string]string `json:"header_fingerprints,omitempty"`
	CookieFingerprints   map[string]string `json:"cookie_fingerprints,omitempty"`
}

// ScreenshotInfo 截图信息
type ScreenshotInfo struct {
	FilePath string `json:"file_path"`
	Format   string `json:"format"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	FileSize int64  `json:"file_size"`
}

// CDNInfo CDN信息
type CDNInfo struct {
	Provider   string            `json:"provider"`
	Confidence int               `json:"confidence"`
	Headers    map[string]string `json:"headers"`
	CNames     []string          `json:"cnames"`
}

// CollectionDNSResponse DNS响应信息
type CollectionDNSResponse struct {
	ARecords      []string          `json:"a_records"`
	DNSServerUsed string            `json:"dns_server_used"`
	IPToASN       IPASNInfo         `json:"ip_to_asn"`
	CDNHeaders    map[string]string `json:"cdn_headers"`
	RawHeaders    map[string]string `json:"raw_headers"`
	CDNInfo       *CDNInfo          `json:"cdn_info,omitempty"`
}

// CompanyizedWhoisResq WHOIS公司信息
type CompanyizedWhoisResq struct {
	CompanyName        string `json:"company_name"`
	LegalPerson        string `json:"legal_person"`
	Status             string `json:"status"`
	RegistrationNumber string `json:"registration_number"`
}

// CollectionWhoisResq WHOIS响应信息
type CollectionWhoisResq struct {
	RawWhois       string                `json:"raw_whois"`
	WhoisRuntimeMs int                   `json:"whois_runtime_ms"`
	Success        bool                  `json:"success"`
	Company        *CompanyizedWhoisResq `json:"company,omitempty"`
}

// CollectionDomainResq 域名响应信息
type CollectionDomainResq struct {
	Domain     string `json:"domain"`
	IP         string `json:"ip"`
	IsIPAccess bool   `json:"is_ip_access"`
	TLSPem     string `json:"tls_pem"`
	OpenSSLRaw string `json:"openssl_raw"`
}

// AdvancedAnalysisInfo 高级分析信息
type AdvancedAnalysisInfo struct {
	GeoLocation    *GeoLocationInfo    `json:"geo_location,omitempty"`
	TLSSecurity    *TLSSecurityInfo    `json:"tls_security,omitempty"`
	WebFingerprint *WebFingerprintInfo `json:"web_fingerprint,omitempty"`
	Screenshot     *ScreenshotInfo     `json:"screenshot,omitempty"`
}

// MCPResponse MCP响应
type MCPResponse struct {
	CollectionDomainResq
	CollectionWhoisResq
	CollectionDNSResponse
	AdvancedAnalysis *AdvancedAnalysisInfo `json:"advanced_analysis,omitempty"`
}

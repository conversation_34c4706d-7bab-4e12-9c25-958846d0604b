package utils

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// ScreenshotFormat 截图格式
type ScreenshotFormat string

const (
	// FormatPNG PNG格式
	FormatPNG ScreenshotFormat = "png"
	// FormatJPEG JPEG格式
	FormatJPEG ScreenshotFormat = "jpeg"
)

// ScreenshotOptions 截图选项
type ScreenshotOptions struct {
	Format       ScreenshotFormat // 截图格式
	OutputDir    string           // 输出目录
	Width        int              // 浏览器窗口宽度
	Height       int              // 浏览器窗口高度
	FullPage     bool             // 是否捕获整个页面
	WaitTime     int              // 等待页面加载的时间（秒）
	Quality      int              // 图片质量（仅适用于JPEG）
	UseChrome    bool             // 是否使用Chrome（如果为false则使用Firefox）
	ChromeBinary string           // Chrome可执行文件路径（可选）
}

// DefaultScreenshotOptions 返回默认截图选项
func DefaultScreenshotOptions() ScreenshotOptions {
	return ScreenshotOptions{
		Format:    FormatPNG,
		OutputDir: "screenshots",
		Width:     1280,
		Height:    800,
		FullPage:  true,
		WaitTime:  5,
		Quality:   80,
		UseChrome: true,
	}
}

// CaptureScreenshot 捕获网站截图
func CaptureScreenshot(url string, options ScreenshotOptions) (string, error) {
	// 确保输出目录存在
	if err := os.MkdirAll(options.OutputDir, 0755); err != nil {
		return "", fmt.Errorf("创建输出目录失败: %v", err)
	}

	// 生成输出文件名
	timestamp := time.Now().Format("20060102-150405")
	domain := extractDomain(url)
	filename := fmt.Sprintf("%s-%s.%s", domain, timestamp, options.Format)
	outputPath := filepath.Join(options.OutputDir, filename)

	// 根据选择的浏览器执行不同的截图命令
	var err error
	if options.UseChrome {
		err = captureWithChrome(url, outputPath, options)
	} else {
		err = captureWithFirefox(url, outputPath, options)
	}

	if err != nil {
		return "", err
	}

	Logger.Info("已保存网站截图: %s", outputPath)
	return outputPath, nil
}

// captureWithChrome 使用Chrome/Chromium捕获截图
func captureWithChrome(url, outputPath string, options ScreenshotOptions) error {
	// 查找Chrome可执行文件
	chromeBinary := options.ChromeBinary
	if chromeBinary == "" {
		var err error
		chromeBinary, err = findChromeBinary()
		if err != nil {
			return err
		}
	}

	// 构建Chrome命令行参数
	args := []string{
		"--headless",
		"--disable-gpu",
		"--no-sandbox",
		"--disable-setuid-sandbox",
		fmt.Sprintf("--window-size=%d,%d", options.Width, options.Height),
		"--hide-scrollbars",
	}

	// 如果使用代理，添加代理设置
	if IsProxyEnabled() {
		proxyConfig := GetProxyConfig()
		if proxyConfig.URL != "" {
			args = append(args, fmt.Sprintf("--proxy-server=%s", proxyConfig.URL))
		}
	}

	// 添加截图参数
	args = append(args, "--screenshot="+outputPath)
	
	// 设置JPEG质量（如果适用）
	if options.Format == FormatJPEG {
		args = append(args, fmt.Sprintf("--screenshot-jpeg-quality=%d", options.Quality))
	}

	// 添加URL
	args = append(args, url)

	// 创建上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(options.WaitTime+10)*time.Second)
	defer cancel()

	// 执行命令
	Logger.Debug("执行Chrome截图命令: %s %s", chromeBinary, strings.Join(args, " "))
	cmd := exec.CommandContext(ctx, chromeBinary, args...)
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		return fmt.Errorf("Chrome截图失败: %v\n输出: %s", err, string(output))
	}

	return nil
}

// captureWithFirefox 使用Firefox捕获截图
func captureWithFirefox(url, outputPath string, options ScreenshotOptions) error {
	// 查找Firefox可执行文件
	firefoxBinary, err := findFirefoxBinary()
	if err != nil {
		return err
	}

	// 创建临时配置文件目录
	tempProfileDir, err := os.MkdirTemp("", "firefox-profile")
	if err != nil {
		return fmt.Errorf("创建Firefox临时配置文件目录失败: %v", err)
	}
	defer os.RemoveAll(tempProfileDir)

	// 构建Firefox命令行参数
	args := []string{
		"--headless",
		"--screenshot=" + outputPath,
		"--window-size=" + fmt.Sprintf("%d,%d", options.Width, options.Height),
		"--profile", tempProfileDir,
	}

	// 添加URL
	args = append(args, url)

	// 创建上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(options.WaitTime+10)*time.Second)
	defer cancel()

	// 执行命令
	Logger.Debug("执行Firefox截图命令: %s %s", firefoxBinary, strings.Join(args, " "))
	cmd := exec.CommandContext(ctx, firefoxBinary, args...)
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		return fmt.Errorf("Firefox截图失败: %v\n输出: %s", err, string(output))
	}

	return nil
}

// findChromeBinary 查找Chrome/Chromium可执行文件
func findChromeBinary() (string, error) {
	// 常见的Chrome/Chromium可执行文件路径
	possiblePaths := []string{
		// Linux
		"/usr/bin/google-chrome",
		"/usr/bin/chromium-browser",
		"/usr/bin/chromium",
		// macOS
		"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
		"/Applications/Chromium.app/Contents/MacOS/Chromium",
		// Windows
		"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
		"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	// 尝试在PATH中查找
	if path, err := exec.LookPath("google-chrome"); err == nil {
		return path, nil
	}
	if path, err := exec.LookPath("chromium"); err == nil {
		return path, nil
	}
	if path, err := exec.LookPath("chromium-browser"); err == nil {
		return path, nil
	}
	if path, err := exec.LookPath("chrome"); err == nil {
		return path, nil
	}

	return "", fmt.Errorf("未找到Chrome/Chromium可执行文件")
}

// findFirefoxBinary 查找Firefox可执行文件
func findFirefoxBinary() (string, error) {
	// 常见的Firefox可执行文件路径
	possiblePaths := []string{
		// Linux
		"/usr/bin/firefox",
		// macOS
		"/Applications/Firefox.app/Contents/MacOS/firefox",
		// Windows
		"C:\\Program Files\\Mozilla Firefox\\firefox.exe",
		"C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe",
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path, nil
		}
	}

	// 尝试在PATH中查找
	if path, err := exec.LookPath("firefox"); err == nil {
		return path, nil
	}

	return "", fmt.Errorf("未找到Firefox可执行文件")
}

// extractDomain 从URL中提取域名
func extractDomain(url string) string {
	// 移除协议前缀
	url = strings.TrimPrefix(url, "http://")
	url = strings.TrimPrefix(url, "https://")
	
	// 提取域名部分（移除路径和查询参数）
	if idx := strings.Index(url, "/"); idx > 0 {
		url = url[:idx]
	}
	
	// 移除端口号
	if idx := strings.Index(url, ":"); idx > 0 {
		url = url[:idx]
	}
	
	// 替换不允许在文件名中使用的字符
	url = strings.ReplaceAll(url, ".", "-")
	
	return url
}
